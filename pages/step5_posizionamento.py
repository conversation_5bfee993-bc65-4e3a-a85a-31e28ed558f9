from utils.auth import get_auth_manager
import streamlit as st
from components.help_chat import show_help_chat
from utils.credits import get_user_id, get_user_credits

# Require authentication
get_auth_manager().require_auth()


import os
import streamlit as st
from agents.posizionamento_agent import genera_posizionamento

from utils.config import config



# Progress bar function
def show_progress():
    """Display progress bar"""
    current = st.session_state.get("current_step", 5)
    st.progress(min(current / 11, 1.0), text=f"{int(current / 11 * 100)}% completato")

show_progress()

st.markdown(
    """

    <h1 style=" text-align:center;">Step 5: Posizionamento Editoriale</h1>
    <h2 style="text-align:center;">
        Strategia editoriale basata sull'analisi del mercato e del pubblico target.
    </h2>

""",
    unsafe_allow_html=True,
)


# Check prerequisites but don't block navigation
missing_prerequisites = []
if "analisi_argomento_generata" not in st.session_state:
    missing_prerequisites.append("Step 2: Analisi Argomento")
if "buyer_persona_generata" not in st.session_state:
    missing_prerequisites.append("Step 4: Buyer Persona")
if "recensioni_analizzate" not in st.session_state:
    missing_prerequisites.append("Step 3: Analisi Recensioni")

if missing_prerequisites:
    st.warning(f"⚠️ Completa prima: {', '.join(missing_prerequisites)}")
    prerequisites_met = False
else:
    prerequisites_met = True

if prerequisites_met and st.button("📝 Genera Posizionamento Editoriale (8 crediti)", type="primary"):
    user_credits = get_user_credits()
    # Check credits before generation
    if user_credits < 8:
        st.error("❌ Crediti insufficienti! La generazione del posizionamento richiede 8 crediti.")
        st.info("💳 Puoi acquistare più crediti dalla dashboard.")
        st.stop()

    with st.spinner("Elaborazione strategia editoriale..."):
        try:
            user_id = get_user_id()
            data_sources = {
                "Analisi Keyword": st.session_state["analisi_argomento_generata"],
                "Buyer Persona": st.session_state["buyer_persona_generata"],
                "Problemi Recensioni": st.session_state.get(
                    "recensioni_analizzate"
                ),
            }
            prompt = (
                "Sei un esperto di marketing editoriale e posizionamento per libri su Amazon KDP. "
                "Analizza: analisi keyword, buyer persona dettagliata, problemi delle recensioni. "
                "Genera un posizionamento che includa: pubblico target, differenziazione, value proposition, messaggio, tono di voce, canali, obiezioni, rischi, suggerimenti per titolo e struttura. "
                "Rispondi in italiano, con sezioni ben distinte, titoli in grassetto, link in Markdown."
            )
            risposta = genera_posizionamento(
                data_sources,
                config.OPENAI_API_KEY,
                prompt_custom=prompt,
            )
            st.session_state["posizionamento_editoriale"] = risposta

            # Deduct credits for successful generation
            get_auth_manager().deduct_credits(user_id, "content_generation", 8, description="Positioning strategy generation")

            # Save step 5 data
            current_project_id = st.session_state.get("current_project_id")
            if current_project_id:
                step_data = {
                    "posizionamento_editoriale": risposta
                }
                get_auth_manager().save_step_data(user_id, current_project_id, 5, step_data)

            st.success("✅ Posizionamento generato con successo!")
        except Exception as e:
            st.error(f"❌ Errore durante la generazione AI: {str(e)}")

if "posizionamento_editoriale" in st.session_state:
    st.markdown("---")

    st.markdown(
        st.session_state["posizionamento_editoriale"], unsafe_allow_html=True
    )

    # Add regenerate option
    if prerequisites_met and st.button("🔄 Rigenera Posizionamento (8 crediti)", use_container_width=True):
        st.session_state.pop("posizionamento_editoriale", None)
        st.rerun()

# Always show navigation at the bottom
st.markdown("---")
col1, col2 = st.columns(2)

with col1:
    if st.button("⬅️ Indietro", use_container_width=True):
        st.session_state["current_step"] = 4
        st.switch_page("pages/step4_buyer_persona.py")

with col2:
    # Only show continue button if posizionamento has been generated
    if "posizionamento_editoriale" in st.session_state:
        if st.button("✅ Conferma e continua", type="primary", use_container_width=True):
            # Ensure data is saved before proceeding
            user_id = get_user_id()
            current_project_id = st.session_state.get("current_project_id")
            if current_project_id and not st.session_state.get("step5_saved"):
                step_data = {
                    "posizionamento_editoriale": st.session_state.get("posizionamento_editoriale", "")
                }
                result = get_auth_manager().save_step_data(user_id, current_project_id, 5, step_data)
                if result.get("success"):
                    st.session_state["step5_saved"] = True
            st.session_state["current_step"] = 6
            st.switch_page("pages/step6_titolo_sottotitolo.py")
    else:
        st.info("📝 Genera il posizionamento editoriale per continuare")

# Show help chat for step5
show_help_chat("step5_posizionamento")

import streamlit as st
from utils.auth import get_auth_manager
from utils.config import config
from openai import OpenAI
import os



# Require authentication
get_auth_manager().require_auth()

def get_user_info():
    """Get user data and ensure user_id is valid"""
    user_data = st.session_state.get("user_data", {})
    user_id = user_data.get("id")

    # Ensure user_id is valid
    if not user_id:
        st.error("User ID not found. Please login again.")
        st.switch_page("pages/auth_login.py")

    return user_data, user_id





# Title
st.markdown(
    """
    <h1 style='text-align: center; color: #222; font-size: 3em;'>🎯 Vision Builder per Autori</h1>
    <h3 style='text-align: center; color: #444;'>Definisci il tuo Tone of Voice unico e la tua missione come autore</h3>
    """,
    unsafe_allow_html=True,
)

# Initialize session state for vision builder
if "vision_step" not in st.session_state:
    st.session_state["vision_step"] = 1
if "vision_questions" not in st.session_state:
    st.session_state["vision_questions"] = []
if "vision_answers" not in st.session_state:
    st.session_state["vision_answers"] = ["", "", ""]
if "generated_vision" not in st.session_state:
    st.session_state["generated_vision"] = ""

# Progress bar function
def show_progress():
    """Display progress bar"""
    current_step = st.session_state["vision_step"]
    progress = min(current_step / 5, 1.0)  # 5 steps total
    st.progress(progress, text=f"Step {current_step} of 5 - {int(progress * 100)}% Complete")

show_progress()

def generate_vision_questions():
    """Generate 3 psychological questions using OpenAI"""

    # Fallback questions
    return [
        "Quali sono le tue passioni?",
        "Quali sono i tuoi obiettivi?",
        "Quali sono gli hobby che fai nel tempo libero?"
    ]

def generate_author_vision(questions, answers):
    """Generate author vision statement from answers"""
    try:
        client = OpenAI(api_key=config.OPENAI_API_KEY)

        prompt = f"""Based on the following questions and answers, generate an "Author Vision" statement that:
        • Identifies who this author is (values, motivations, identity)
        • Describes their editorial tone (voice, style, core values)
        • Summarizes their key goals and the impact they wish to create
        • Defines key marketing strategies and tactics to achieve their goals based on their unique perspective and audience

        Write the vision as a cohesive paragraph of 5-10 sentences. Do not quote the answers, but build them into a professional, inspiring vision statement that helps author understand the author's unique perspective and motivates and suggests him the path to achieve his goals and reach his/her perfect target audience.

        Questions and Answers:
        1. {questions[0]}
        Answer: {answers[0]}

        2. {questions[1]}
        Answer: {answers[1]}

        3. {questions[2]}
        Answer: {answers[2]}

        Generate the vision statement in a professional, confident tone and in Italian Language:"""

        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": "You are an expert author coach who craft deep insights about writers into compelling 5-10 paragraphs vision statements and top notch editiorial marketing strategies. Use Italian language."},
                {"role": "user", "content": prompt}
            ],

            max_tokens=3000
        )

        content = response.choices[0].message.content
        return content.strip() if content else "Unable to generate vision statement. Please try again."
    except Exception as e:
        st.error(f"Error generating vision: {str(e)}")
        return "Unable to generate vision statement. Please try again."

# Step 1: Generate Questions
if st.session_state["vision_step"] == 1:

    if not st.session_state["vision_questions"]:
        with st.spinner("Crafting your personalized questions..."):
            questions = generate_vision_questions()
            st.session_state["vision_questions"] = questions



    st.markdown("### Conosciamoci")
    st.text("💡  Più informazioni ci permetteranno ad aiutarti a creare eBook non-fiction di successo!")

    if st.button("🚀Inizia a scoprire il tuo vision statement", type="primary"):
        st.session_state["vision_step"] = 2
        st.rerun()

# Steps 2-4: Answer Questions
elif st.session_state["vision_step"] in [2, 3, 4]:
    question_index = st.session_state["vision_step"] - 2
    questions = st.session_state["vision_questions"]

    if question_index < len(questions):
        st.markdown(f"### Question {question_index + 1} of 3")
        st.markdown(f"##### {questions[question_index]}")


        # Current answer form
        with st.form(f"question_{question_index + 1}_form"):
            st.markdown("💭 **Prenditi il tuo tempo e sii dettagliato:**")
            answer = st.text_area(
                "La tua risposta",
                value=st.session_state["vision_answers"][question_index],
                height=200,
                placeholder="Condividi le tue riflessioni, esperienze, sogni e aspirazioni...",
                help="Più dettagli fornirai, meglio potrai creare la tua visione."
            )

            col1, col2 = st.columns([1, 1])
            with col1:
                if question_index > 0:
                    back_button = st.form_submit_button("⬅️ Previous Question")
                else:
                    back_button = False
            with col2:
                continue_button = st.form_submit_button(
                    "Next Question ➡️" if question_index < 2 else "Generate My Vision 🎯",
                    type="primary"
                )
            # Show previous answers for context
            if question_index > 0:
                for i in range(question_index):
                    st.markdown(f"**Q{i+1}:** {questions[i]}")
                    st.markdown(f"**A{i+1}:** {st.session_state['vision_answers'][i]}")
            if continue_button:
                if not answer or not answer.strip():
                    st.error("Please provide an answer before continuing.")
                elif len(answer.strip()) < 50:
                    st.error("Please provide a more detailed answer (at least 50 characters).")
                else:
                    st.session_state["vision_answers"][question_index] = answer.strip()
                    if question_index < 2:
                        st.session_state["vision_step"] = st.session_state["vision_step"] + 1
                        st.rerun()
                    else:
                        st.session_state["vision_step"] = 5
                        st.rerun()

            if back_button and question_index > 0:
                st.session_state["vision_step"] = st.session_state["vision_step"] - 1
                st.rerun()

# Step 5: Generate vision and auto-redirect
elif st.session_state["vision_step"] == 5:
    # Get user info when needed
    user_data, user_id = get_user_info()
    
    # Generate vision
    with st.spinner("Crafting your unique author vision..."):
        vision = generate_author_vision(st.session_state["vision_questions"], st.session_state["vision_answers"])
        st.session_state["generated_vision"] = vision

        # Save to database
        saved = get_auth_manager().save_vision_builder(
            str(user_id),
            st.session_state["vision_questions"],
            st.session_state["vision_answers"],
            vision
        )

        if saved:
            st.success("✅ Your author vision has been created and saved!")
            st.balloons()

            # Show the vision
            st.markdown("### 🎯 Your Author Vision")
            st.markdown(f"*{vision}*")

            # Update user profile to mark onboarding as completed
            get_auth_manager().update_user_profile(str(user_id), {
                "onboarding_completed": True,
                "vision_completed": True
            })

            # Force refresh user data to reflect the updated vision status
            get_auth_manager().refresh_user_data(str(user_id))

            if st.button("📚 Create Editorial Roadmap", type="primary", use_container_width=True):
                st.switch_page("pages/editorial_roadmap.py")
        else:
            st.error("Failed to save your vision. Please try again.")


st.markdown(
    f"""
    <div style="text-align: center; color: #666; font-size: 0.8em;">
        <p>Welcome, {st.session_state.get("user_data", {}).get('name', 'Author')}! Scopriamo insieme il tuo potenziale come autore.</p>
    </div>
    """,
    unsafe_allow_html=True,
)

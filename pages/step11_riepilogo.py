from utils.auth import get_auth_manager
from utils.credits import get_user_credits, get_user_id
from components.help_chat import show_help_chat

# Require authentication
get_auth_manager().require_auth()
import streamlit as st




# Progress bar function
def show_progress():
    """Display progress bar"""
    st.progress(
        min(11 / 12, 1.0),
        text=f"{int(11 / 12 * 100)}% completato",
    )

show_progress()

st.markdown(
    """
    <div style="background-color:#e8f5e9; padding:30px; border-radius:12px; border:1px solid #a5d6a7; text-align: center;">
        <h2 style="color:#388e3c; font-size:28px;">📦 Step 11: Riepilogo del Progetto Editoriale</h2>
        <p style="color:#37474f; font-size:16px;">
            Tutto il tuo lavoro raccolto in un unico posto: dall’analisi iniziale alla strategia finale.
        </p>
    </div>
""",
    unsafe_allow_html=True,
)

st.markdown("---")



def mostra_sezione(titolo, contenuto, tipo="info", formattato=False, icona=None):
    title = f"{icona} {titolo}" if icona else titolo
    st.subheader(title)
    if not contenuto:
        st.warning(f"{titolo} non disponibile.")
        return
    if formattato:
        st.markdown(contenuto, unsafe_allow_html=True)
    else:
        box = st.container()
        if tipo == "info":
            box.info(contenuto)
        elif tipo == "success":
            box.success(contenuto)
        elif tipo == "warning":
            box.warning(contenuto)
        else:
            box.write(contenuto)

mostra_sezione(
    "1. 🔍 Argomento / Keyword", st.session_state.get("argomento_keyword")
)
# Handle recensioni separately to avoid markdown formatting issues
st.subheader("2. ✍️ Recensioni Analizzate")
if st.session_state.get("report_recensioni"):
    st.markdown("**Analisi delle recensioni:**")
    st.markdown(st.session_state.get("report_recensioni"))
if st.session_state.get("recensioni_inserite"):
    recensioni = st.session_state.get("recensioni_inserite", [])
    if isinstance(recensioni, list):
        recensioni_text = "\n\n".join(recensioni)
    else:
        recensioni_text = str(recensioni)
    st.text(recensioni_text)
mostra_sezione(
    "3. 👤 Buyer Persona Generata",
    st.session_state.get("buyer_persona_generata"),
    tipo="success",
    formattato=True,
)
mostra_sezione(
    "4. 🎯 Posizionamento Editoriale",
    st.session_state.get("posizionamento_editoriale"),
    tipo="info",
    formattato=True,
)
mostra_sezione("5. 🏷️ Titolo", st.session_state.get("titolo_scelto"))
mostra_sezione("6. ✨ Sottotitolo", st.session_state.get("sottotitolo_scelto"))
mostra_sezione(
    "7. 📘 Indice del Libro",
    st.session_state.get("indice_libro_generato"),
    tipo="success",
    formattato=True,
)
mostra_sezione(
    "8. 🛒 Descrizione Amazon",
    st.session_state.get("descrizione_amazon"),
    tipo="info",
    formattato=True,
)
# Cover section with image and suggestions
st.subheader("9. 🎨 Cover Ideale")

# Function to get cover URL from various sources
def get_cover_url_from_session():
    # List of all possible keys to check
    url_keys = ["cover_url", "cover_image_url", "generated_cover_url", "final_cover_url"]

    # First try direct session state with all possible keys
    for key in url_keys:
        url = st.session_state.get(key)
        if url:
            return url

    # Try step_data (loaded from projects.py)
    step_data = st.session_state.get("step_data", {})

    # Check step10b data with all possible keys
    step10b = step_data.get("step10b", {})
    for key in url_keys:
        url = step10b.get(key)
        if url:
            return url

    # Check step10 data (sometimes cover URL might be here)
    step10 = step_data.get("step10", {})
    for key in url_keys:
        url = step10.get(key)
        if url:
            return url

    # Check step11 data
    step11 = step_data.get("step11", {})
    summary_data = step11.get("summary_data", {})
    for key in url_keys:
        url = summary_data.get(key)
        if url:
            return url

    # Try project data at root level
    project_data = st.session_state.get("project_data", {})
    for key in url_keys:
        url = project_data.get(key)
        if url:
            return url

    # Try final result
    final_result = st.session_state.get("final_result", {})
    if isinstance(final_result, str):
        try:
            import json
            final_result = json.loads(final_result)
        except:
            final_result = {}
    if isinstance(final_result, dict):
        for key in url_keys:
            url = final_result.get(key)
            if url:
                return url

    # Check current project from database (fallback)
    current_project = st.session_state.get("current_project", {})
    for key in url_keys:
        url = current_project.get(key)
        if url:
            return url

    return None

# Function to get cover suggestions from various sources
def get_cover_suggestions_from_session():
    # List of all possible keys for cover suggestions
    suggestion_keys = ["cover_suggerita", "cover_suggestions", "cover_suggestion", "cover_report", "cover_ideas"]

    # First try direct session state with all possible keys
    for key in suggestion_keys:
        suggestions = st.session_state.get(key)
        if suggestions:
            return suggestions

    # Try step_data (loaded from projects.py)
    step_data = st.session_state.get("step_data", {})

    # Check step10 data with all possible keys
    step10 = step_data.get("step10", {})
    for key in suggestion_keys:
        suggestions = step10.get(key)
        if suggestions:
            return suggestions

    # Also check for selected_cover_idea in step10
    selected_idea = step10.get("selected_cover_idea")
    if selected_idea:
        return selected_idea

    # Check step10b data with all possible keys
    step10b = step_data.get("step10b", {})
    for key in suggestion_keys:
        suggestions = step10b.get(key)
        if suggestions:
            return suggestions

    # Also check for selected_cover_idea in step10b
    selected_idea = step10b.get("selected_cover_idea")
    if selected_idea:
        return selected_idea

    # Check step11 data
    step11 = step_data.get("step11", {})
    summary_data = step11.get("summary_data", {})
    for key in suggestion_keys:
        suggestions = summary_data.get(key)
        if suggestions:
            return suggestions

    # Try summary_data directly in session state
    summary = st.session_state.get("summary_data", {})
    for key in suggestion_keys:
        suggestions = summary.get(key)
        if suggestions:
            return suggestions

    # Try project data at root level
    project_data = st.session_state.get("project_data", {})
    for key in suggestion_keys:
        suggestions = project_data.get(key)
        if suggestions:
            return suggestions

    # Try final result
    final = st.session_state.get("final_result", {})
    if isinstance(final, str):
        try:
            import json
            final = json.loads(final)
        except:
            final = {}
    if isinstance(final, dict):
        for key in suggestion_keys:
            suggestions = final.get(key)
            if suggestions:
                return suggestions

    # Check current project from database (fallback)
    current_project = st.session_state.get("current_project", {})
    for key in suggestion_keys:
        suggestions = current_project.get(key)
        if suggestions:
            return suggestions

    return None

# Check if cover was skipped
cover_skipped = st.session_state.get("cover_skipped", False)

# Display cover image
display_cover_url = get_cover_url_from_session()
if display_cover_url:
    st.markdown("**Cover Generata:**")
    try:
        st.image(display_cover_url, caption="Cover del Libro", width=300)
    except Exception as e:
        st.warning(f"Impossibile caricare l'immagine: {str(e)}")
elif cover_skipped:
    st.info("ℹ️ Generazione cover saltata - Puoi aggiungere la cover in seguito")
else:
    st.warning("Immagine cover non disponibile.")

# Display cover suggestions
display_cover_suggestions = get_cover_suggestions_from_session()
if display_cover_suggestions:
    st.markdown("**Suggerimenti Cover dall'AI:**")
    st.markdown(display_cover_suggestions, unsafe_allow_html=True)
elif not cover_skipped:
    st.warning("Suggerimenti cover non disponibili.")

st.markdown("---")

st.markdown("---")
col1, col2 = st.columns(2)

with col1:
    if st.button("⬅️ Indietro", use_container_width=True):
        st.session_state["current_step"] = 10.5
        st.switch_page("pages/step10b_design_cover.py")

with col2:
    if st.button("✅ Conferma e continua", type="primary", use_container_width=True):
        # Check credits before summary compilation


        # Deduct credits for summary compilation
        user_id = get_user_id()

        # Save step 11 completion and update project status
        try:
            current_project_id = st.session_state.get("current_project_id")

            if current_project_id:
                # Prepare step 11 data
                step_data = {
                    "riepilogo_completed": True,
                    "summary_data": {
                        "title": st.session_state.get("titolo_scelto", ""),
                        "subtitle": st.session_state.get("sottotitolo_scelto", ""),
                        "topic": st.session_state.get("argomento_keyword", ""),
                        "marketplace": st.session_state.get("marketplace", "IT"),
                        "language": st.session_state.get("lingua_target", "it"),
                        "buyer_persona": st.session_state.get("buyer_persona_generata", ""),
                        "positioning": st.session_state.get("posizionamento_editoriale", ""),
                        "table_of_contents": st.session_state.get("indice_libro_generato", ""),
                        "amazon_description": st.session_state.get("descrizione_amazon", ""),
                        "cover_suggestion": st.session_state.get("cover_suggerita", ""),
                        "reviews_analysis": st.session_state.get("report_recensioni", "")
                    }
                }

                # Use modular save method
                user_id = get_user_id()
                result = get_auth_manager().save_step_data(user_id, current_project_id, 11, step_data)
                if result.get("success"):
                    st.session_state["current_step"] = 12
                    st.success("✅ Progetto completato e salvato!")
                else:
                    st.error(f"Error saving step completion: {result.get('error', 'Unknown error')}")
            else:
                st.warning("Nessun progetto corrente trovato. I dati saranno salvati nella sessione.")
                st.session_state["current_step"] = 12

        except Exception as e:
            st.error(f"Error saving project completion: {str(e)}")

        st.switch_page("pages/step12_scarica_word.py")

# Show help chat for step11
show_help_chat("step11_riepilogo")

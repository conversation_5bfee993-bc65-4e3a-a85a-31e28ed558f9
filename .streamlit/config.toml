# .streamlit/config.toml  –  Theme “KDP Stratega”
# .streamlit/config.toml  —  KDP Stratega theme
[theme]
base = "light"

# ─── Palette principale ─────────────────────────────────────────────
primaryColor = "#2F80ED"             # “Electric SaaS Blue” – call-to-action
linkColor = "#2F80ED"
backgroundColor = "#F8FAFC"          # Ultra-light slate background
secondaryBackgroundColor = "#FFFFFF" # Card & widget fill
codeBackgroundColor = "#F1F5F9"      # Slight slate tint for code
textColor = "#1E293B"                # Slate 800 - better contrast
borderColor = "#E2E8F0"              # Slate 200 - subtle borders

# ─── Typography & sizing ────────────────────────────────────────────
font = "Inter, sans-serif"
headingFont = "Poppins, sans-serif"
codeFont = "JetBrains Mono, Consolas, monospace"
baseFontSize = 14                                # Reduced for better density
baseRadius = "6px"                               # Slightly smaller radius
showWidgetBorder = true
showSidebarBorder = false

# ─── Component sizing ───────────────────────────────────────────────
[theme.components]
# Metrics styling
metricPadding = "0.5rem 0.75rem"
metricBorderRadius = "6px"
metricFontSize = "0.875rem"
metricValueSize = "1.5rem"

# Button styling
buttonPadding = "0.5rem 1rem"
buttonBorderRadius = "6px"
buttonFontSize = "0.875rem"
buttonFontWeight = "500"

# Input styling
inputPadding = "0.5rem 0.75rem"
inputBorderRadius = "6px"
inputFontSize = "0.875rem"

# ─── Google Fonts (weight prefetch) ─────────────────────────────────
[[theme.fontFaces]]
family = "Inter"
url = "https://fonts.gstatic.com/s/inter/v12/Inter-VariableFont_slnt,wght.ttf"
weight = 400
style = "normal"

[[theme.fontFaces]]
family = "Poppins"
url = "https://fonts.gstatic.com/s/poppins/v20/Poppins-Bold.ttf"
weight = 700
style = "normal"

# ─── Sidebar override (coherent with main UI) ───────────────────────
[theme.sidebar]
backgroundColor = "#FFFFFF"
secondaryBackgroundColor = "#F8FAFC"
primaryColor = "#2F80ED"
linkColor = "#2F80ED"
textColor = "#1E293B"
borderColor = "#E2E8F0"
font = "Inter, sans-serif"
headingFont = "Poppins, sans-serif"
baseRadius = "6px"
showWidgetBorder = true

# ─── Advanced styling ───────────────────────────────────────────────
[theme.advanced]
# Spacing system
spacingXs = "0.25rem" # 4px
spacingSm = "0.5rem"  # 8px
spacingMd = "0.75rem" # 12px
spacingLg = "1rem"    # 16px
spacingXl = "1.5rem"  # 24px
spacing2xl = "2rem"   # 32px

# Shadow system
shadowSm = "0 1px 2px 0 rgba(0, 0, 0, 0.05)"
shadowMd = "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"
shadowLg = "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)"

# Animation
transitionFast = "150ms ease-in-out"
transitionNormal = "200ms ease-in-out"
transitionSlow = "300ms ease-in-out"

[client]
showSidebarNavigation = false

[server]
enableStaticServing = true

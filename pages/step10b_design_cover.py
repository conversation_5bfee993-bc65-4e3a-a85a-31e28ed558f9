import streamlit as st
from utils.auth import get_auth_manager
from utils.config import config
from openai import OpenAI
import base64
from io import BytesIO
from PIL import Image
import streamlit.components.v1 as components
import uuid
from utils.credits import get_user_credits, get_user_id
from components.help_chat import show_help_chat
import requests
import gc
from utils.memory_optimization import memory_cleanup, cleanup_large_objects, optimize_image_memory, get_memory_optimizer
LINGUE = {
    "it": "italiano",
    "en": "inglese",
    "es": "spagnolo",
    "fr": "francese",
    "de": "tedesco",
}
# Require authentication
get_auth_manager().require_auth()

# Check if step 10 is completed and idea selected
if "cover_suggerita" not in st.session_state or "selected_cover_idea_content" not in st.session_state:
    st.error("❌ Completa prima lo Step 10 e seleziona un'idea cover.")
    st.switch_page("pages/step10_cover_perfetta.py")
    st.stop()

# Progress bar
st.progress(10.5/12, text=f"Step 10B of 12 - {int(10.5/12 * 100)}% Complete")

st.markdown(
    """
    <h1 style='text-align: center;'>🎨 Step 10B: Crea la Tua Cover</h1>
    <h3 style='text-align: center;'>Trasforma l'idea selezionata in una cover professionale</h3>
    """,
    unsafe_allow_html=True,
)



# Get book data function
def get_book_data():
    """Get book data from session state"""
    return {
        "title": st.session_state.get("titolo_scelto", ""),
        "subtitle": st.session_state.get("sottotitolo_scelto", ""),
        "author_name": st.session_state.get("user_data", {}).get("name", "Author Name"),
        "selected_idea": st.session_state.get("selected_cover_idea_content", "")
    }

# Display selected idea
book_data = get_book_data()
st.info(f"💡 Idea selezionata: #{st.session_state.get('selected_cover_idea_index', 0) + 1}")
with st.expander("Dettagli dell'idea selezionata", expanded=False):
    st.markdown(book_data["selected_idea"])

# Initialize session state
if "design_method" not in st.session_state:
    st.session_state["design_method"] = None
if "cover_url" not in st.session_state:
    st.session_state["cover_url"] = None
if "manual_base_image_url" not in st.session_state:
    st.session_state["manual_base_image_url"] = None
if "manual_base_generated" not in st.session_state:
    st.session_state["manual_base_generated"] = False

def upload_to_supabase(image_data, filename):
    """Upload image to Supabase storage and return URL"""
    try:
        auth_mgr = get_auth_manager()

        if hasattr(auth_mgr, 'conn') and auth_mgr.conn:
            # Create unique filename
            unique_filename = f"{uuid.uuid4()}_{filename}"

            # Upload to storage bucket
            result = auth_mgr.conn.storage.from_("covers").upload(
                path=unique_filename,
                file=image_data,
                file_options={"content-type": "image/png", "upsert": "true"}
            )

            # Get public URL
            public_url = auth_mgr.conn.storage.from_("covers").get_public_url(unique_filename)
            return public_url
        else:
            st.error("Nessuna connessione Supabase disponibile")
            return None
    except Exception as e:
        st.error(f"Errore generale upload: {str(e)}")
        return None

@memory_cleanup
def generate_cover_with_dalle(prompt, include_text=True, pen_name=None):
    """Generate cover using GPT Image API"""
    try:
        client = OpenAI(api_key=config.OPENAI_API_KEY)
        book_data = get_book_data()

        # Use pen name if provided, otherwise use original author name
        author_name = pen_name if pen_name else book_data['author_name']

        # Create detailed prompt
        if include_text:
            # AI mode: include text
            subtitle_text = f"- Subtitle (medium size): \"{book_data['subtitle']}\"" if book_data['subtitle'] else ""
            full_prompt = f""" You are the most awarded book cover designer that sold millions of copies on amazon thanks to their impact, understanding of the audience
            {st.session_state["buyer_persona_generata"]} and {st.session_state["posizionamento_editoriale"]}
            Create a professional book cover design that maximize click through rate and sales with the following specifications:

{prompt}

IMPORTANT TEXT TO INCLUDE ON THE COVER:
- Title (large, prominent): "{book_data['title']}"
{subtitle_text}
- Author name (bottom): "{author_name}"

The text should be clearly legible and professionally integrated into the design.

Book cover dimensions: portrait format suitable for Amazon KDP.
IMPORTANT:
- THE LANGUAGE OF THE BOOK IS: {LINGUE.get(st.session_state["lingua_target"])}
- PAY EXTREMELY ATTENTION TO NAMING AND TYPOS!!!!
"""
        else:
            # Manual mode: full cover design without text
            full_prompt = f""" You are the most awarded book cover designer that sold millions of copies on amazon thanks to their impact, understanding of the audience
            {st.session_state["buyer_persona_generata"]} and {st.session_state["posizionamento_editoriale"]}
            Create a professional book cover design that maximize click through rate and sales with the following specifications:

{prompt}

AVOID ANY REFERENE TO FONTS - THIS IS THE COVER DESIGN ONLY
DO NOT ADD ANY TEXT ON THE COVER

Book cover dimensions: portrait format suitable for Amazon KDP.
Make this as visually stunning and professional as possible
"""

        # Generate image
        result = client.images.generate(
            model="gpt-image-1",
            prompt=full_prompt,
            size="1024x1536",  # Portrait format
            quality="high",
            output_format="jpeg"

        )

        # Get base64 data and convert to bytes
        if result and result.data and len(result.data) > 0:
            image_base64 = result.data[0].b64_json
            if image_base64:
                image_bytes = base64.b64decode(image_base64)
                # Optimize image memory if too large
                optimized_bytes = optimize_image_memory(image_bytes, max_size_mb=5.0)
                if optimized_bytes:
                    return optimized_bytes
                return image_bytes
            else:
                st.error("❌ Nessun dato base64 ricevuto dall'API")
                return None
        else:
            st.error("❌ Risposta API vuota o invalida")
            return None

    except Exception as e:
        st.error(f"Errore generazione immagine: {str(e)}")
        return None

def render_canvas_editor(background_url=None):
    """Render the canvas editor for manual design"""
    # Get book data inside function
    book_data = get_book_data()
    title = book_data["title"]
    subtitle = book_data["subtitle"]
    author_name = book_data["author_name"]

    # Escape strings for JavaScript
    safe_title = (title if title else "Title").replace("`", "\\`").replace("${", "\\${")
    safe_subtitle = (subtitle if subtitle else "Subtitle").replace("`", "\\`").replace("${", "\\${")
    safe_author = (author_name if author_name else "Author Name").replace("`", "\\`").replace("${", "\\${")

    # Create safe filename
    safe_filename = title.replace("'", "").replace('"', "").replace(":", "").replace(" ", "_").replace("/", "_").replace("\\", "_")

    st.markdown("### 🎨 Editor Manuale Cover")
    st.markdown("**Personalizza il testo della tua cover**")

    # Check if we have a generated background
    if background_url and background_url.startswith("data:image"):
        # Use the full data URL directly
        bg_url = background_url
        # Scaled down size for easier editing (maintains 2:3 aspect ratio)
        w, h = 400, 600
    else:
        st.error("❌ Nessuno sfondo generato. Torna indietro e genera prima lo sfondo.")
        return

    # Google Fonts
    fonts = [
        "Roboto", "Playfair Display", "Merriweather", "Lora", "Montserrat",
        "Oswald", "Poppins", "Raleway", "Inconsolata"
    ]
    fonts_query = "|".join(
        f"{name.replace(' ', '+')}:ital,wght@0,400;0,700;1,400;1,700" for name in fonts
    )

    # Build the HTML
    html = f"""
    <link href='https://fonts.googleapis.com/css?family={fonts_query}&display=swap' rel='stylesheet'>
    <script src='https://ajax.googleapis.com/ajax/libs/webfont/1.6.26/webfont.js'></script>
    <script src='https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js'></script>
    <style>
      canvas {{ border:1px solid #999; }}
      #ctrls {{ text-align:center; margin-top:16px; }}
      .fieldCtrl {{ display:flex; flex-direction:column; gap:6px; align-items:center; margin-bottom:14px; }}
      .fieldRow  {{ display:flex; flex-wrap:wrap; gap:8px; justify-content:center; align-items:center; }}
      .fieldRow label {{ font-weight:600; margin-right:4px; }}
      .fieldRow select, .fieldRow input[type=number] {{ padding:2px 4px; }}
      #saveStatus {{ margin-top: 20px; padding: 10px; display: none; }}
    </style>

    <canvas id='c'></canvas>
    <div id='ctrls'></div>
    <div id='saveStatus'></div>

    <script>
      const coverW = {w};
      const coverH = {h};
      const fontList = {fonts};

      // Load Google Fonts
      WebFont.load({{ google: {{ families: fontList.map(f => `${{f}}:ital,wght@0,400;0,700;1,400;1,700`) }} }});

      // Fabric canvas
      const canvas = new fabric.Canvas('c', {{ selection: false }});
      canvas.setWidth(coverW);
      canvas.setHeight(coverH);

      // Background image - scale to fit canvas
      fabric.Image.fromURL('{bg_url}', img => {{
        img.selectable = false;

        // Scale image to fit canvas while maintaining aspect ratio
        const scaleX = coverW / img.width;
        const scaleY = coverH / img.height;
        const scale = Math.max(scaleX, scaleY); // Use max to ensure full coverage

        img.scaleX = scale;
        img.scaleY = scale;

        // Center the image if it's larger than the canvas
        img.originX = 'center';
        img.originY = 'center';

        canvas.setBackgroundImage(img, canvas.renderAll.bind(canvas), {{
          left: coverW / 2,
          top: coverH / 2,
          originX: 'center',
          originY: 'center'
        }});
      }});

      // Pre-define fields with actual book data (scaled for smaller canvas)
      const FIELDS = [
        {{ key: 'title',    label: `{safe_title}`,    y: coverH * 0.15, size: 32, color: '#FFFFFF', weight: 700, style: 'normal' }},
        {{ key: 'subtitle', label: `{safe_subtitle}`, y: coverH * 0.35, size: 20, color: '#DDDDDD', weight: 400, style: 'normal' }},
        {{ key: 'author',   label: `{safe_author}`,   y: coverH * 0.85, size: 16, color: '#FFFFFF', weight: 400, style: 'normal' }}
      ];

      // Create Fabric textboxes
      FIELDS.forEach(f => {{
        const tb = new fabric.Textbox(f.label, {{
          left: coverW * 0.1,
          top: f.y,
          width: coverW * 0.8,
          textAlign: 'center',
          fontFamily: fontList[0],
          fontSize: f.size,
          fill: f.color,
          fontWeight: String(f.weight),
          fontStyle: f.style,
          editable: true
        }});
        tb.set('fieldKey', f.key);
        canvas.add(tb);
      }});

      // UI controls container
      const ctrls = document.getElementById('ctrls');

      function makeCtrlRow(field) {{
        const wrapper = document.createElement('div');
        wrapper.className = 'fieldCtrl';

        // --- primary row (text, color, font, size, weight, style) ---------
        const row1 = document.createElement('div');
        row1.className = 'fieldRow';
        row1.innerHTML = `
          <label>${{field.key.charAt(0).toUpperCase() + field.key.slice(1)}}:</label>
          <input id="${{field.key}}Txt" type="text" value="${{field.label}}" style="width:220px;">
          <input id="${{field.key}}Col" type="color" value="${{field.color}}">
          <select id="${{field.key}}Font"></select>
          <input id="${{field.key}}Size" type="number" min="10" max="300" value="${{field.size}}" style="width:70px;"> px
        `;
        wrapper.appendChild(row1);

        // Weight selector
        const weightSel = document.createElement('select');
        weightSel.id = `${{field.key}}Weight`;
        [100,200,300,400,500,600,700,800,900].forEach(wgt => {{
          const opt = document.createElement('option');
          opt.value = wgt;
          opt.textContent = wgt;
          if (wgt === field.weight) opt.selected = true;
          weightSel.appendChild(opt);
        }});
        row1.appendChild(weightSel);

        // Style selector
        const styleSel = document.createElement('select');
        styleSel.id = `${{field.key}}Style`;
        ['normal','italic'].forEach(sty => {{
          const opt = document.createElement('option');
          opt.value = sty;
          opt.textContent = sty.charAt(0).toUpperCase() + sty.slice(1);
          if (sty === field.style) opt.selected = true;
          styleSel.appendChild(opt);
        }});
        row1.appendChild(styleSel);

        // --- shadow controls row -----------------------------------------
        const row2 = document.createElement('div');
        row2.className = 'fieldRow';
        row2.innerHTML = `
          <label>Ombra:</label>
          <input type="checkbox" id="${{field.key}}ShadowToggle">
          <input type="color" id="${{field.key}}ShadowColor" value="#000000">
          Sfocatura <input type="number" id="${{field.key}}ShadowBlur" value="5" min="0" max="50" style="width:60px;">
          X <input type="number" id="${{field.key}}ShadowX" value="2" min="-100" max="100" style="width:60px;">
          Y <input type="number" id="${{field.key}}ShadowY" value="2" min="-100" max="100" style="width:60px;">
        `;
        wrapper.appendChild(row2);
        ctrls.appendChild(wrapper);

        // Populate font select
        const fontSel = row1.querySelector(`#${{field.key}}Font`);
        fontList.forEach(fn => {{
          const opt = document.createElement('option');
          opt.value = fn;
          opt.textContent = fn;
          opt.style.fontFamily = fn;
          fontSel.appendChild(opt);
        }});

        // Helpers to access DOM controls
        const textbox   = () => canvas.getObjects('textbox').find(o => o.fieldKey === field.key);
        const txtInp    = row1.querySelector(`#${{field.key}}Txt`);
        const colInp    = row1.querySelector(`#${{field.key}}Col`);
        const sizeInp   = row1.querySelector(`#${{field.key}}Size`);

        const shadowToggle = row2.querySelector(`#${{field.key}}ShadowToggle`);
        const shadowColor  = row2.querySelector(`#${{field.key}}ShadowColor`);
        const shadowBlur   = row2.querySelector(`#${{field.key}}ShadowBlur`);
        const shadowX      = row2.querySelector(`#${{field.key}}ShadowX`);
        const shadowY      = row2.querySelector(`#${{field.key}}ShadowY`);

        const apply = () => {{
          const tb = textbox();
          if (!tb) return;
          if (txtInp.value !== '') tb.text = txtInp.value;
          tb.set({{
            fill: colInp.value,
            fontFamily: fontSel.value,
            fontSize: parseInt(sizeInp.value),
            fontWeight: weightSel.value,
            fontStyle: styleSel.value
          }});

          if (shadowToggle.checked) {{
            tb.set('shadow', new fabric.Shadow({{
              color: shadowColor.value,
              blur: parseInt(shadowBlur.value),
              offsetX: parseInt(shadowX.value),
              offsetY: parseInt(shadowY.value)
            }}));
          }} else {{
            tb.set('shadow', null);
          }}

          canvas.renderAll();
        }};

        // Event listeners
        [txtInp, colInp, fontSel, weightSel, styleSel, sizeInp, shadowToggle, shadowColor, shadowBlur, shadowX, shadowY].forEach(el => el.oninput = apply);
      }}

      FIELDS.forEach(makeCtrlRow);

      // Buttons container
      const btnContainer = document.createElement('div');
      btnContainer.style.marginTop = '20px';
      btnContainer.style.display = 'flex';
      btnContainer.style.gap = '10px';
      btnContainer.style.justifyContent = 'center';

      // Download button
      const dl = document.createElement('button');
      dl.textContent = 'Scarica PNG';
      dl.style.padding = '8px 16px';
      dl.style.backgroundColor = '#4CAF50';
      dl.style.color = 'white';
      dl.style.border = 'none';
      dl.style.borderRadius = '4px';
      dl.style.cursor = 'pointer';
      dl.onclick = () => {{
        const url = canvas.toDataURL({{ format: 'png', multiplier: 2.56 }});
        const a = document.createElement('a');
        a.href = url;
        a.download = 'cover_{safe_filename}.png';
        a.click();
      }};
      btnContainer.appendChild(dl);

      // Save to project button
      const saveBtn = document.createElement('button');
      saveBtn.textContent = 'Salva nel Progetto';
      saveBtn.style.padding = '8px 16px';
      saveBtn.style.backgroundColor = '#2196F3';
      saveBtn.style.color = 'white';
      saveBtn.style.border = 'none';
      saveBtn.style.borderRadius = '4px';
      saveBtn.style.cursor = 'pointer';
      saveBtn.onclick = () => {{
        const url = canvas.toDataURL({{ format: 'png', multiplier: 2.56 }});
        // Convert to blob
        fetch(url)
          .then(res => res.blob())
          .then(blob => {{
            // Show saving status
            const status = document.getElementById('saveStatus');
            status.style.display = 'block';
            status.style.backgroundColor = '#2196F3';
            status.style.color = 'white';
            status.innerHTML = 'Salvataggio in corso...';

            // Send to Streamlit
            const reader = new FileReader();
            reader.onloadend = function() {{
              window.parent.postMessage({{
                type: 'canvas_save',
                imageData: reader.result
              }}, '*');
            }};
            reader.readAsDataURL(blob);
          }});
      }};
      btnContainer.appendChild(saveBtn);

      ctrls.appendChild(btnContainer);
    </script>
    """

    # Render the HTML component
    components.html(html, height=h + 500, width=w + 60)

# Main interface
st.markdown("---")
st.subheader("🎯 Scegli come creare la tua cover")

col1, col2 = st.columns(2)

with col1:
    st.markdown("### 🤖 Generazione AI")
    st.markdown("""
    - Cover generata automaticamente
    - Include titolo, sottotitolo e nome autore
    - Basata sull'idea selezionata
    - 10 crediti
    """)

    if st.button("🎨 Genera con AI", type="primary", use_container_width=True):
        st.session_state["design_method"] = "ai"

with col2:
    st.markdown("### ✏️ Editor Manuale")
    st.markdown("""
    - AI genera sfondo senza testo
    - Tu aggiungi e personalizzi il testo
    - Controllo completo su font e stili
    - 10 crediti
    """)

    if st.button("✏️ Genera Base + Editor", use_container_width=True):
        st.session_state["design_method"] = "manual"

# Show selected method
if st.session_state["design_method"] == "ai":
    st.markdown("---")
    st.subheader("🤖 Generazione AI della Cover")

    # Pen name input for AI generation
    pen_name = st.text_input(
        "✍️ Usa un nome diverso per questa cover (opzionale):",
        value="",
        placeholder=f"Lascia vuoto per usare: {st.session_state.get('user_data', {}).get('name', 'Author Name')}",
        help="Questo nome sarà usato solo per questa cover e non verrà salvato nel database."
    )

    # Display current author name that will be used
    current_author = pen_name.strip() if pen_name.strip() else st.session_state.get("user_data", {}).get("name", "Author Name")
    st.info(f"👤 Nome autore che apparirà sulla cover: **{current_author}**")

    # Check credits
    if get_user_credits() < 10:
        st.error("❌ Crediti insufficienti! La generazione AI richiede 10 crediti.")
        st.info("💳 Puoi acquistare più crediti dalla dashboard.")
    else:
        # Check if we should generate (using a flag to avoid re-generation on rerun)
        if "ai_generation_triggered" not in st.session_state:
            st.session_state["ai_generation_triggered"] = False

        with st.form("ai_generation_form"):
            book_data = get_book_data()
            st.markdown("**La cover includerà automaticamente:**")
            st.markdown(f"- Titolo: {book_data['title']}")
            st.markdown(f"- Sottotitolo: {book_data['subtitle']}")
            st.markdown(f"- Autore: {current_author}")

            additional_prompt = st.text_area(
                "Aggiungi dettagli specifici per il design (opzionale):",
                placeholder="Es: stile minimalista, colori caldi, atmosfera misteriosa...",
                height=100,
                key="ai_additional_prompt_input"
            )

            generate_button = st.form_submit_button("🎨 Genera Cover (10 crediti)", type="primary")

            if generate_button:
                st.session_state["ai_generation_triggered"] = True
                st.session_state["ai_additional_prompt"] = additional_prompt

        # Process generation outside of form to ensure session state persists
        if st.session_state.get("ai_generation_triggered", False) and not st.session_state.get("cover_url"):
            with st.spinner("🎨 Generazione cover in corso... (può richiedere 30-60 secondi)"):
                # Refresh book data to get latest selection
                book_data = get_book_data()
                full_prompt = book_data["selected_idea"]
                if not full_prompt:
                    st.error("❌ Nessuna idea cover selezionata!")
                    st.session_state["ai_generation_triggered"] = False
                    st.stop()
                # Get additional prompt from session state (saved from form)
                additional_prompt = st.session_state.get("ai_additional_prompt", "")
                if additional_prompt:
                    full_prompt += f"\n\nAdditional design requirements: {additional_prompt}"

                image_bytes = generate_cover_with_dalle(full_prompt, include_text=True, pen_name=pen_name.strip() if pen_name.strip() else None)

                if image_bytes:
                    # Save to session state immediately (we'll get URL after upload)
                    st.session_state["ai_generation_triggered"] = False

                    # Display generated image
                    st.success("✅ Cover generata con successo!")
                    st.image(image_bytes, caption="Cover Generata", use_column_width=True)

                    # Clean up memory after displaying
                    gc.collect()

                    # Always show download button
                    book_data = get_book_data()
                    st.download_button(
                        label="📥 Scarica Cover",
                        data=image_bytes,
                        file_name=f"cover_{book_data['title'].replace(' ', '_')}.png",
                        mime="image/png",
                        key="download_ai_cover"
                    )

                    # Try to upload to Supabase
                    filename = f"cover_{st.session_state.get('current_project_id', 'unknown')}.png"
                    public_url = upload_to_supabase(image_bytes, filename)

                    if public_url:
                        st.session_state["cover_url"] = public_url
                        st.success("✅ Cover salvata su Supabase!")
                    else:
                        # Create a temporary data URL if upload fails
                        import base64
                        b64_image = base64.b64encode(image_bytes).decode()
                        data_url = f"data:image/png;base64,{b64_image}"
                        st.session_state["cover_url"] = data_url
                        st.warning("⚠️ Upload su Supabase fallito, ma puoi comunque scaricare e procedere.")
                        # Clean up the raw bytes after encoding
                        cleanup_large_objects(image_bytes)

                    # Deduct credits (do this regardless of upload success)
                    user_id = get_user_id()
                    try:
                        get_auth_manager().deduct_credits(user_id, "cover_generation", 10, description="AI cover generation with DALL-E")
                    except Exception as e:
                        st.warning(f"⚠️ Errore deduzione crediti: {str(e)}")

                    # Save to project (try even if upload failed)
                    current_project_id = st.session_state.get("current_project_id")
                    if current_project_id:
                        step_data = {
                            "cover_design_completed": True,
                            "design_method": "ai_generation",
                            "cover_url": st.session_state["cover_url"],
                            "cover_prompt": full_prompt,
                            "upload_successful": public_url is not None,
                            "cover_suggerita": st.session_state.get("cover_suggerita", ""),
                            "selected_cover_idea": st.session_state.get("selected_cover_idea_content", "")
                        }
                        try:
                            get_auth_manager().save_step_data(user_id, current_project_id, '10b', step_data)
                        except Exception as e:
                            st.warning(f"⚠️ Impossibile salvare i dati del progetto: {str(e)}")
                else:
                    st.error("❌ Generazione cover fallita")
                    st.session_state["ai_generation_triggered"] = False

        # Show existing cover if already generated
        elif st.session_state.get("cover_url") and st.session_state["design_method"] == "ai":
            st.success("✅ Cover già generata!")
            st.image(st.session_state["cover_url"], caption="Cover Generata", use_column_width=True)

            # Regenerate button
            if st.button("🔄 Rigenera Cover (10 crediti)", use_container_width=True):
                st.session_state["cover_url"] = None
                st.session_state["ai_generation_triggered"] = True
                st.rerun()

elif st.session_state["design_method"] == "manual":
    st.markdown("---")
    st.subheader("✏️ Editor Manuale con Base AI")

    # Generate base image immediately if not already done
    if not st.session_state.get("manual_base_generated"):
        # Check credits only when we need to generate
        if get_user_credits() < 10:
            st.error("❌ Crediti insufficienti! La generazione della base AI richiede 10 crediti.")
            st.info("💳 Puoi acquistare più crediti dalla dashboard.")
        else:
            with st.spinner("🎨 Generazione sfondo AI in corso... (può richiedere 30-60 secondi)"):
                # Generate background without text
                book_data = get_book_data()
                full_prompt = book_data["selected_idea"]

                image_bytes = generate_cover_with_dalle(full_prompt, include_text=False)

                if image_bytes:
                    # Convert to data URL immediately for canvas
                    import base64
                    b64_image = base64.b64encode(image_bytes).decode()
                    data_url = f"data:image/png;base64,{b64_image}"
                    st.session_state["manual_base_image_url"] = data_url
                    st.session_state["manual_base_generated"] = True

                    # Clean up the raw bytes after encoding
                    cleanup_large_objects(image_bytes)
                    gc.collect()

                    # Deduct credits
                    user_id = get_user_id()
                    get_auth_manager().deduct_credits(user_id, "cover_base_generation", 10, description="Manual mode base image generation")

                    st.rerun()

    # Show editor with generated background
    if st.session_state.get("manual_base_generated") and st.session_state.get("manual_base_image_url"):
        # Show canvas editor directly
        render_canvas_editor(background_url=st.session_state["manual_base_image_url"])

        # Regenerate button below canvas
        st.markdown("---")
        if st.button("🔄 Rigenera Sfondo (10 crediti)", use_container_width=True):
            st.session_state["manual_base_generated"] = False
            st.session_state.pop("manual_base_image_url", None)
            st.rerun()

    # Handle canvas save via JavaScript postMessage
    st.markdown("""
    <script>
    window.addEventListener('message', function(e) {
        if (e.data.type === 'canvas_save' && e.data.imageData) {
            // Convert base64 to blob
            const base64Data = e.data.imageData.split(',')[1];
            const byteCharacters = atob(base64Data);
            const byteNumbers = new Array(byteCharacters.length);
            for (let i = 0; i < byteCharacters.length; i++) {
                byteNumbers[i] = byteCharacters.charCodeAt(i);
            }
            const byteArray = new Uint8Array(byteNumbers);
            const blob = new Blob([byteArray], {type: 'image/png'});

            // Store in session for Streamlit to access
            window.canvasSaveData = e.data.imageData;

            // Update status
            const status = document.getElementById('saveStatus');
            if (status) {
                status.style.backgroundColor = '#4CAF50';
                status.innerHTML = '✅ Cover pronta per il salvataggio! Clicca il pulsante sotto.';
            }
        }
    });
    </script>
    """, unsafe_allow_html=True)

    # Manual save button
    if st.button("💾 Conferma e Salva Cover Manuale", key="manual_save"):
        # Check if we have canvas data (this would need proper implementation with components)
        # For now, save the project data
        user_id = get_user_id()
        current_project_id = st.session_state.get("current_project_id")
        if current_project_id:
            step_data = {
                "cover_design_completed": True,
                "design_method": "manual_editor",
                "base_image_url": st.session_state.get("manual_base_image_url", ""),
                "cover_edited": True,
                "cover_suggerita": st.session_state.get("cover_suggerita", ""),
                "selected_cover_idea": st.session_state.get("selected_cover_idea_content", "")
            }
            get_auth_manager().save_step_data(user_id, current_project_id, '10b', step_data)
            st.session_state["cover_url"] = st.session_state.get("manual_base_image_url", "")  # Temporary until canvas save works
            st.success("✅ Cover salvata nel progetto!")
            st.info("💡 Per salvare le modifiche del canvas, usa il pulsante 'Scarica PNG' nell'editor.")

# Navigation buttons
st.markdown("---")
col1, col2, col3 = st.columns(3)

with col1:
    if st.button("⬅️ Torna allo Step 10", use_container_width=True):
        # Clear step 10b specific state when going back
        st.session_state.pop("design_method", None)
        st.session_state.pop("cover_url", None)
        st.session_state.pop("manual_base_image_url", None)
        st.session_state.pop("manual_base_generated", None)
        st.session_state.pop("ai_generation_triggered", None)
        st.session_state.pop("ai_additional_prompt", None)
        st.session_state["current_step"] = 10
        st.switch_page("pages/step10_cover_perfetta.py")

with col2:
    if st.button("⏭️ Salta Cover", use_container_width=True, type="secondary"):
        # Allow skipping cover generation
        st.session_state["cover_design_completed"] = True
        st.session_state["cover_skipped"] = True
        st.session_state["current_step"] = 11

        # Save step data indicating cover was skipped
        current_project_id = st.session_state.get("current_project_id")
        if current_project_id:
            step_data = {
                "cover_url": "",
                "cover_image_url": "",
                "design_method": "skipped",
                "cover_design_completed": True,
                "cover_skipped": True,
                "cover_suggerita": st.session_state.get("cover_suggerita", ""),
                "selected_cover_idea": st.session_state.get("selected_cover_idea_content", "")
            }
            user_id = get_user_id()
            get_auth_manager().save_step_data(user_id, current_project_id, '10b', step_data)

        st.switch_page("pages/step11_riepilogo.py")

with col3:
    if st.session_state.get("cover_url") or st.session_state.get("design_method") == "manual":
        if st.button("➡️ Procedi al Riepilogo", type="primary", use_container_width=True):
            # Save cover data before proceeding
            current_project_id = st.session_state.get("current_project_id")
            if current_project_id:
                step_data = {
                    "cover_url": st.session_state.get("cover_url", ""),
                    "cover_image_url": st.session_state.get("cover_image_url", ""),
                    "design_method": st.session_state.get("design_method", ""),
                    "cover_design_completed": True,
                    "cover_skipped": False,
                    "cover_suggerita": st.session_state.get("cover_suggerita", ""),
                    "selected_cover_idea": st.session_state.get("selected_cover_idea_content", ""),
                    "manual_base_image_url": st.session_state.get("manual_base_image_url", "")
                }
                user_id = get_user_id()
                get_auth_manager().save_step_data(user_id, current_project_id, '10b', step_data)

            st.session_state["current_step"] = 11
            st.switch_page("pages/step11_riepilogo.py")
    else:
        st.info("💡 Completa la creazione della cover per procedere")

# Show help chat for step10b
show_help_chat("step10b_design_cover")

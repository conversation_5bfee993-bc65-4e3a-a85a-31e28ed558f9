
import stripe
import streamlit as st
from utils.config import config
from utils.auth import get_auth_manager
import json
class StripeManager:
    def __init__(self):
        self.stripe_secret_key = config.STRIPE_SECRET_KEY
        if self.stripe_secret_key:
            stripe.api_key = self.stripe_secret_key

    def create_credit_checkout_session(self, user_id: str, credit_package: dict, success_url: str, cancel_url: str):
        """Create a Stripe checkout session for credit purchase"""
        try:
            if not self.stripe_secret_key:
                raise Exception("Stripe secret key not configured")

            # Create line item for credit package
            line_items = [{
                'price_data': {
                    'currency': 'eur',
                    'product_data': {
                        'name': f"{credit_package['credits']} Credits",
                        'description': f"Purchase {credit_package['credits']} AI analysis credits" +
                                     (f" + {credit_package['bonus']} bonus credits!" if credit_package.get('bonus', 0) > 0 else ""),
                        'images': [],
                    },
                    'unit_amount': int(credit_package['price'] * 100),  # Convert to cents
                },
                'quantity': 1,
            }]

            # Create checkout session
            session = stripe.checkout.Session.create(
                payment_method_types=['card'],
                line_items=line_items, #type: ignore
                mode='payment',
                success_url=success_url,
                cancel_url=cancel_url,
                client_reference_id=user_id,
                metadata={
                    'user_id': user_id,
                    'credits': credit_package['credits'],
                    'bonus_credits': credit_package.get('bonus', 0),
                    'total_credits': credit_package['credits'] + credit_package.get('bonus', 0),
                    'package_type': 'credits'
                }
            )

            return session

        except Exception as e:
            st.error(f"Error creating Stripe session: {str(e)}")
            return None



    def retrieve_session(self, session_id: str):
        """Retrieve a Stripe checkout session"""
        try:
            if not self.stripe_secret_key:
                raise Exception("Stripe secret key not configured")

            session = stripe.checkout.Session.retrieve(session_id)
            return session

        except Exception as e:
            st.error(f"Error retrieving session: {str(e)}")
            return None

    def handle_successful_payment(self, session_id: str):
        """Handle successful payment and update user credits/subscription"""
        try:

            session = self.retrieve_session(session_id)
            if not session:
                st.error("🔍 DEBUG - Failed to retrieve session")
                return False



            if session.payment_status == 'paid':
                user_id = session.metadata.get('user_id') #type: ignore
                package_type = session.metadata.get('package_type') #type: ignore


                if package_type == 'credits':
                    # Handle credit purchase
                    total_credits = int(session.metadata.get('total_credits', 0)) #type: ignore


                    # Update user credits in database
                    success = get_auth_manager().add_user_credits(user_id, total_credits) #type: ignore

                    # st.info(f"🔍 DEBUG - Credit update success: {success}")

                    if success:
                        # Record transaction
                        transaction_data = {
                            'user_id': user_id,
                            'stripe_session_id': session_id,
                            'amount': session.amount_total / 100,  # Convert from cents #type: ignore
                            'currency': session.currency,
                            'credits_purchased': total_credits,
                            'transaction_type': 'credit_purchase',
                            'status': 'completed'
                        }

                        # st.info(f"🔍 DEBUG - Recording transaction: {transaction_data}")
                        transaction_success = get_auth_manager().record_transaction(transaction_data)
                        # st.info(f"🔍 DEBUG - Transaction record success: {transaction_success}")

                        return True
                else:
                    st.error(f"🔍 DEBUG - Unknown package_type: {package_type}")
            else:
                st.error(f"🔍 DEBUG - Payment not paid, status: {session.payment_status}")

            return False

        except Exception as e:
            st.error(f"Error handling payment: {str(e)}")
            st.error(f"🔍 DEBUG - Exception details: {type(e).__name__}: {str(e)}")
            return False

    def create_customer_portal_session(self, customer_id: str, return_url: str):
        """Create a customer portal session for managing billing"""
        try:
            if not self.stripe_secret_key:
                raise Exception("Stripe secret key not configured")

            session = stripe.billing_portal.Session.create(
                customer=customer_id,
                return_url=return_url,
            )

            return session

        except Exception as e:
            st.error(f"Error creating customer portal session: {str(e)}")
            return None

    def validate_webhook(self, payload: str, sig_header: str):
        """Validate Stripe webhook signature"""
        try:
            webhook_secret = config.STRIPE_WEBHOOK_SECRET
            if not webhook_secret:
                raise Exception("Webhook secret not configured")

            event = stripe.Webhook.construct_event(
                payload, sig_header, webhook_secret
            )

            return event

        except Exception as e:
            st.error(f"Error validating webhook: {str(e)}")
            return None

# Global instance
stripe_manager = StripeManager()

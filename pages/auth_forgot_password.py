import streamlit as st
from utils.auth import get_auth_manager
import os
st.markdown(
    """
    <style>
    /* Professional auth form styling */
    .stMainBlockContainer  {
        max-width: 480px !important;
        padding-top: 2rem !important;
        padding-left: 1rem !important;
        padding-right: 1rem !important;
    }


    /* Mobile styling */
    @media (max-width: 480px) {

        h1 {
            font-size: 2.2em !important;
        }

        h3 {
            font-size: 1.1em !important;
        }
    }

    /* Form elements styling */
    .stTextInput > div > div > input {
        font-size: 16px !important;
    }

    .stButton > button {
        width: 100% !important;
        font-weight: 500 !important;
    }
    </style>
    """,
    unsafe_allow_html=True,
)
# ──────

# Title
st.markdown(
    """
    <h1 style='text-align: center; color: #222; font-size: 3em;'>🔑 Reset Password</h1>
    <h3 style='text-align: center; color: #444;'>Get back into your KDP GENIUS account</h3>
    """,
    unsafe_allow_html=True,
)

# Check if user is already authenticated
if st.session_state.get("authenticated", False):
    st.success("Sei già loggato!")
    user_data = st.session_state.get("user_data", {})
    st.info(f"Welcome, {user_data.get('name', 'User')}!")

    col1, col2 = st.columns(2)
    with col1:
        if st.button("📊 Vai alla Dashboard", type="primary"):
            st.switch_page("pages/dashboard/overview.py")
    with col2:
        if st.button("📚 Inizia un nuovo libro"):
            st.switch_page("pages/dashboard/projects.py")
    st.stop()

# Password reset form
with st.container():
    st.markdown("### Inserisci il tuo indirizzo email")
    st.info("Ti invieremo le istruzioni per reimpostare la password.")

    with st.form("forgot_password_form"):
        email = st.text_input("Email", placeholder="<EMAIL>")

        col1, col2 = st.columns([1, 1])
        with col1:
            reset_button = st.form_submit_button("📧 Invia link di reset", type="primary", use_container_width=True)
        with col2:
            back_button = st.form_submit_button("⬅️ Torna al login", use_container_width=True)

    # Handle password reset
    if reset_button:
        if not email:
            st.error("per favore inserisci l'email")
        else:
            with st.spinner("Ti stiamo inviando il link di reset..."):
                try:
                    # Get auth manager and send reset email
                    auth_mgr = get_auth_manager()
                    supabase = auth_mgr.conn

                    # Send password reset email
                    # Redirect to landing page that will convert hash to query params
                    response = supabase.auth.reset_password_for_email(
                        email=email,
                        options={
                            "redirect_to": "https://kdpgenius.com/auth-redirect.html"
                        }
                    )

                    st.success(f"Se esiste già un account con {email}, abbiamo inviato le istruzioni per reimpostare la password al tuo indirizzo email.")
                    st.info("Controlla la tua casella di posta elettronica e la cartella spam per il link di reset .")

                    with st.expander("📧 Cosa succede adesso?"):
                        st.markdown("""
                        1. controlla la tua casella di posta elettronica e la cartella spam per il link di reset
                        2. clicca sul link di reset
                        3. verrai reindirizzato a impostare una nuova password
                        4. accedi con le tue nuove credenziali
                        ** Nota:** Il link di reset scade dopo un'ora per motivi di sicurezza.

                        """)

                except Exception as e:

                    st.error(f"Errore inviando il link di reset: {str(e)}")
                    st.info("Perfavore prova ancora o contatta il supporto se il problema persiste.")

    # Handle back to loginåå
    if back_button:
        st.switch_page("pages/auth_login.py")

# Navigation links
st.markdown("---")
col1, col2 = st.columns(2)

with col1:
    st.markdown("**Ti sei ricordato la password?**")
    if st.button("🔐 Accedi", use_container_width=True):
        st.switch_page("pages/auth_login.py")

with col2:
    st.markdown("**Non hai un account?**")
    if st.button("📝 Iscriviti", use_container_width=True):
        st.switch_page("pages/auth_signup.py")

# Footer
st.markdown("---")
st.markdown(
    """
    <div style="text-align: center; color: #666; font-size: 0.8em;">
        <p>© 2024 KDP GENIUS - Your intelligent publishing ally</p>
        <p><a href="#" style="color: #666;">Terms of Service</a> | <a href="#" style="color: #666;">Privacy Policy</a></p>
    </div>
    """,
    unsafe_allow_html=True,
)

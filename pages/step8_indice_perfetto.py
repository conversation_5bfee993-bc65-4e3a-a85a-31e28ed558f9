from utils.auth import get_auth_manager
import streamlit as st
from utils.credits import get_user_credits, get_user_id
from components.help_chat import show_help_chat

# Require authentication
get_auth_manager().require_auth()



import os
import streamlit as st
from agents.indice_agent import genera_indice
from utils.config import config


LINGUE = {
    "it": "italiano",
    "en": "inglese",
    "es": "spagnolo",
    "fr": "francese",
    "de": "tedesco",
}

TIPOLOGIE_LIBRO = [
    "Study Guide",
    "Ricettario",
    "Rimedi Naturali",
    "Manuale DIY",
    "Guida all'Esame",
    "Guida Psicologica",
    "Diario Tematico",
    "Piano Alimentare",
    "Manuale per Genitori",
    "Guida Fitness / Yoga",
    "Educazione Bambini",
    "Crescita Personale",
    "Spiritualità e Religione",
    "Finanza Personale",
    "Business / Start-up",
    "Organizzazione e Produttività"
]

CATEGORIE_CONTENUTO = [
    "<PERSON>ser<PERSON><PERSON>",
    "Ricette",
    "Rimedi Naturali",
    "Spiegazioni teoriche",
    "Strategie pratiche",
    "Checklist",
    "Test e quiz",
    "Storie vere",
    "Tecniche passo-passo",
    "Piani settimanali",
    "Attività giornaliere"
]

TARGET_PUBBLICO = [
    "Principianti",
    "Livello intermedio",
    "Esperti/Professionisti",
    "Studenti universitari",
    "Adulti 25-40 anni",
    "Adulti 40-60 anni",
    "Genitori",
    "Professionisti del settore",
    "Appassionati/Hobbysti",
    "Persone in cerca di crescita personale"
]
# Progress bar function
def show_progress():
    """Display progress bar"""
    st.progress(min(8 / 12, 1.0), text=f"{int(8 / 12 * 100)}% completato")

show_progress()


st.markdown(
    """

        <h1 style="text-align:center;">📖 Step 8: Generatore Indice Intelligente KDP</h1>
        <h3 style="text-align:center;">Sistema avanzato per la generazione automatica di indici multilivello per libri non-fiction</h3>

    """,
    unsafe_allow_html=True,
)

# Display idea analysis results if available
if st.session_state.get("idea_analysis"):
    analysis = st.session_state["idea_analysis"]

    # Show critical issues or warnings that might affect content structure
    if analysis.get("critical_issues") or analysis.get("content_disclaimers"):
        st.markdown("### ⚠️ Considerazioni dall'Analisi del Concept")

        if analysis.get("critical_issues"):
            with st.expander("🚨 Problematiche Critiche da Considerare", expanded=True):
                for issue in analysis["critical_issues"]:
                    st.error(f"❌ {issue}")

        if analysis.get("content_disclaimers"):
            with st.expander("📋 Disclaimer Necessari per i Contenuti", expanded=True):
                for disclaimer in analysis["content_disclaimers"]:
                    st.info(f"📋 {disclaimer}")

        if analysis.get("recommendations"):
            with st.expander("💡 Raccomandazioni per la Struttura", expanded=True):
                relevant_recs = [rec for rec in analysis["recommendations"] if any(keyword in rec.lower() for keyword in ["struttura", "contenuto", "capitolo", "indice", "organizza"])]
                if relevant_recs:
                    for rec in relevant_recs[:3]:  # Show max 3 relevant recommendations
                        st.info(f"💡 {rec}")
                else:
                    st.info("💡 Considera le raccomandazioni generali dell'analisi per ottimizzare la struttura")


# Prerequisiti
required_steps = {
    "argomento_keyword": "Step 2",
    "buyer_persona_generata": "Step 4",
    "posizionamento_editoriale": "Step 5",
    "titolo_scelto": "Step 6",
    "sottotitolo_scelto": "Step 6",
    "idea_analysis": "Step 7 - Idea Analyzer",
}
def validate_prerequisites():
    """Check prerequisites only if moving forward"""
    last_completed = st.session_state.get("last_step_completed", 0)

    # Only validate if we're moving forward (not backward navigation)
    if last_completed < 8:  # Step 8 is the current step
        missing = [
            step for key, step in required_steps.items() if not st.session_state.get(key)
        ]
        if missing:
            st.warning(f"⚠️ Completa prima questi step: {', '.join(missing)}")
            st.stop()

# Run validation
validate_prerequisites()

# Idea analysis will be accessed when needed


# === SEZIONE 1: CONFIGURAZIONE LIBRO ===
st.markdown("### 📚 Configurazione Libro")

col1, col2 = st.columns(2)

with col1:
    tipo_libro = st.selectbox(
        "🎯 Tipologia Libro",
        TIPOLOGIE_LIBRO,
        help="Seleziona il tipo di libro che stai creando"
    )
    num_capitoli = st.slider(
        "📖 Numero Capitoli",
        min_value=3,
        max_value=25,
        value=8,
        help="Numero totale di capitoli del libro"
    )



with col2:
    categoria_contenuto = st.selectbox(
        "📋 Categoria Contenuto Principale",
        CATEGORIE_CONTENUTO,
        help="Tipo di contenuto prevalente nel libro"
    )


# === SEZIONE 2: CONTENUTI QUANTITATIVI ===
st.markdown("### 🔢 Contenuti Quantitativi")

col3, col4 = st.columns(2)

with col3:
    # Selettore per numero di contenuti in multipli di 5
    numero_contenuti = st.selectbox(
        "📊 Numero Totale Contenuti",
        [i for i in range(5, 205, 5)],
        index=19,  # Default 100
        help="Numero totale di elementi (es. 100 esercizi, 50 ricette, etc.)"
    )

with col4:
    livello_profondita = st.selectbox(
        "🔍 Livello di Profondità",
        ["Base (2 livelli)", "Intermedio (3 livelli)", "Avanzato (4 livelli)"],
        index=1,
        help="Profondità della struttura dell'indice"
    )

# === SEZIONE 3: PERSONALIZZAZIONI AVANZATE ===
st.markdown("### ⚙️ Personalizzazioni Avanzate")

col5, col6 = st.columns(2)

with col5:
    includi_introduzione = st.checkbox(
        "📝 Includi Sezione Introduttiva",
        value=True,
        help="Aggiunge una breve sezione introduttiva che spiega scopo e contenuto"
    )

    includi_appendici = st.checkbox(
        "📎 Includi Appendici",
        value=False,
        help="Aggiunge sezioni appendice (glossario, risorse, etc.)"
    )

with col6:


    descrizioni_capitoli = st.checkbox(
        "📄 Descrizioni Capitoli",
        value=True,
        help="Aggiunge brevi descrizioni per ogni capitolo"
    )

# === SEZIONE SPECIALE PER STUDY GUIDE/EXAM PREP ===
if tipo_libro in ["Study Guide", "Guida all'Esame"]:
    st.markdown("### 🎓 Configurazione Speciale Study Guide")
    st.info("ℹ️ Per Study Guide e Guide all'Esame, verrà automaticamente aggiunto un capitolo finale con risposte e soluzioni")


    includi_simulazioni = st.checkbox(
        "🧪 Includi soluzione all'Esame",
        value=True,
        help="Aggiunge sezioni con soluzioni complete d'esame"
    )
    if tipo_libro in ["Study Guide", "Guida all'Esame"]:
        st.write(f"• Include simulazioni: {'Sì' if 'includi_simulazioni' in locals() and includi_simulazioni else 'No'}")


# === ANTEPRIMA CONFIGURAZIONE ===
with st.expander("👁️ Anteprima Configurazione"):
    st.write("**Riepilogo Impostazioni:**")
    st.write(f"• Tipo libro: {tipo_libro}")
    st.write(f"• Target: {st.session_state.get('buyer_persona_generata')}")
    st.write(f"• Contenuto: {categoria_contenuto}")
    st.write(f"• Capitoli: {num_capitoli}")
    st.write(f"• Contenuti totali: {numero_contenuti}")
    st.write(f"• Lingua: {LINGUE.get(st.session_state.get('lingua_target', 'it'))}") #type: ignore


# === GENERAZIONE INDICE ===
if st.button("🚀 Genera Indice Intelligente (8 crediti)", type="primary"):
    # Check credits before generation
    if get_user_credits() < 8:
        st.error("❌ Crediti insufficienti! La generazione dell'indice richiede 8 crediti.")
        st.info("💳 Puoi acquistare più crediti dalla dashboard.")
        st.stop()

    with st.spinner("🧠 Generazione indice intelligente in corso..."):
        try:
            # Calcolo distribuzione contenuti per capitolo
            contenuti_per_capitolo = numero_contenuti // num_capitoli
            contenuti_rimanenti = numero_contenuti % num_capitoli

            # Get idea analysis results
            idea_analysis = st.session_state.get("idea_analysis", {})
            analysis_context = ""
            if idea_analysis:
                critical_issues = idea_analysis.get("critical_issues", [])
                disclaimers = idea_analysis.get("content_disclaimers", [])
                recommendations = idea_analysis.get("recommendations", [])

                if critical_issues or disclaimers or recommendations:
                    analysis_context = f"""

            ANALISI RISCHI E COMPLIANCE (Step 7):
            - Problematiche critiche: {'; '.join(critical_issues) if critical_issues else 'Nessuna'}
            - Disclaimer necessari: {'; '.join(disclaimers) if disclaimers else 'Nessuno'}
            - Raccomandazioni struttura: {'; '.join([r for r in recommendations if any(keyword in r.lower() for keyword in ['struttura', 'contenuto', 'capitolo', 'indice'])]) if recommendations else 'Nessuna'}
            - Livello rischio: {idea_analysis.get('overall_risk_level', 'Non specificato')}
            """

            # Costruzione prompt intelligente
            prompt = f"""
            SPECIFICA TECNICA GENERAZIONE INDICE INTELLIGENTE KDP

            TIPO LIBRO: {tipo_libro}

            ARGOMENTO: {st.session_state['argomento_keyword']}
            TITOLO: {st.session_state.get('titolo_scelto', '')}
            SOTTOTITOLO: {st.session_state.get('sottotitolo_scelto', '')}
            BUYER PERSONA: {st.session_state.get('buyer_persona_generata', '')}
            POSIZIONAMENTO: {st.session_state.get('posizionamento_editoriale', '')}
            LINGUA: {LINGUE.get(st.session_state.get("lingua_target", "it"), "italiano")}
            {analysis_context}
            DISCLAIMER NECESSARI: {st.session_state.get('idea_analysis', {}).get('content_disclaimers', [])}

            STRUTTURA RICHIESTA:
            - {num_capitoli} capitoli numerati (Capitolo 1, Capitolo 2, etc.)
            - Livello profondità: {livello_profondita}
            - IMPORTANTE {numero_contenuti} {categoria_contenuto.lower()} totali - assicurati che siano equamente distribuiti
            - IMPORTANTE Distribuzione: circa {contenuti_per_capitolo} {categoria_contenuto.lower()} per capitolo - assicurati che siano equamente distribuiti per capitoli!!

            {"SEZIONE INTRODUTTIVA: Includi una breve sezione introduttiva che spiega scopo e contenuto del libro." if includi_introduzione else ""}
            {"DESCRIZIONI: Aggiungi brevi descrizioni per ogni capitolo principale." if descrizioni_capitoli else ""}
            {"APPENDICI: Includi sezioni appendice (glossario, risorse aggiuntive, bibliografia)." if includi_appendici else ""}

            COMPORTAMENTO INTELLIGENTE:
            - Suddividi logicamente i {numero_contenuti} {categoria_contenuto.lower()} in base al numero di capitoli
            - Usa numerazione gerarchica (1.1, 1.2, 1.3 per sotto-capitoli, 1.1.1, 1.1.2 per sotto-sotto-capitoli)
            - Mantieni coerenza tematica all'interno di ogni capitolo
            - Progressione logica da base ad avanzato
            - IMPORTANTE: Considera i risultati dell'analisi rischi per evitare contenuti problematici
            - Se ci sono disclaimer necessari, includi sezioni appropriate per gestirli
            - Struttura i contenuti per minimizzare i rischi identificati nell'analisi

            {f"ECCEZIONE STUDY GUIDE: Aggiungi come ultimo capitolo 'Capitolo {num_capitoli + 1}: Risposte e Soluzioni' che conterrà tutte le soluzioni spiegate degli esercizi del libro." if tipo_libro in ["Study Guide", "Guida all'Esame"] else ""}

            CONSIDERAZIONI SPECIFICHE DALL'ANALISI:
            {"- Evita contenuti che potrebbero violare le policy KDP identificate" if idea_analysis.get('critical_issues') else ""}
            {"- Includi sezioni per disclaimer necessari identificati nell'analisi" if idea_analysis.get('content_disclaimers') else ""}
            {"- Struttura i contenuti considerando le raccomandazioni di compliance" if idea_analysis.get('recommendations') else ""}

            Genera un indice dettagliato, ben strutturato e professionale in formato Markdown.
            Rispondi SOLO con l'indice in Markdown, senza commenti aggiuntivi.
            """

            indice = genera_indice(
                dati={
                    "Argomento": st.session_state["argomento_keyword"],
                    "Target": st.session_state.get('buyer_persona_generata'),
                    "TipoLibro": tipo_libro,
                    "NumeroContenuti": numero_contenuti,
                    "NumCapitoli": num_capitoli,
                    "CategoriaContenuto": categoria_contenuto
                },
                stile=tipo_libro,
                openai_key=config.OPENAI_API_KEY,
                prompt_custom=prompt,
                lingua=LINGUE.get(st.session_state.get("lingua_target", "it"), "italiano")

            )

            st.session_state["indice_libro_generato"] = indice
            st.session_state["indice_configurazione"] = {
                "tipo_libro": tipo_libro,
                "target_pubblico": st.session_state.get('buyer_persona_generata'),
                "categoria_contenuto": categoria_contenuto,
                "numero_contenuti": numero_contenuti,
                "num_capitoli": num_capitoli,
                "lingua": LINGUE.get(st.session_state.get("lingua_target", "it"), "italiano")

            }

            # Deduct credits for successful generation
            user_id = get_user_id()
            get_auth_manager().deduct_credits(user_id, "content_generation", 8, description="Table of contents generation")

            # Save step 8 data
            current_project_id = st.session_state.get("current_project_id")
            if current_project_id:
                step_data = {
                    "indice_libro_generato": indice,
                    "indice_configurazione": st.session_state["indice_configurazione"]
                }
                user_id = get_user_id()
                get_auth_manager().save_step_data(user_id, current_project_id, 8, step_data)

            st.success("✅ Indice intelligente generato con successo!")

        except Exception as e:
            st.error(f"❌ Errore durante la generazione: {str(e)}")

# === VISUALIZZAZIONE RISULTATO ===
if st.session_state.get("indice_libro_generato"):
    st.markdown("---")
    st.markdown("### 📖 Indice Intelligente Generato")

    # Mostra configurazione utilizzata
    if st.session_state.get("indice_configurazione"):
        config_data = st.session_state["indice_configurazione"]
        with st.expander("🔧 Configurazione Utilizzata"):
            col_a, col_b = st.columns(2)
            with col_a:
                st.write(f"**Tipo:** {config_data['tipo_libro']}")
                st.write(f"**Target:** {config_data['target_pubblico']}")
                st.write(f"**Contenuto:** {config_data['categoria_contenuto']}")
            with col_b:
                st.write(f"**Totale Contenuti:** {config_data['numero_contenuti']}")
                st.write(f"**Capitoli:** {config_data['num_capitoli']}")
                st.write(f"**Lingua:** {config_data['lingua']}")

    # Mostra indice
    st.markdown("#### 📚 Indice Completo:")
    st.markdown(st.session_state["indice_libro_generato"], unsafe_allow_html=True)

    # Add regenerate option
    if st.button("🔄 Rigenera Indice (8 crediti)", use_container_width=True):
        st.session_state.pop("indice_libro_generato", None)
        st.session_state.pop("indice_configurazione", None)
        st.rerun()

    # Navigation buttons
    st.markdown("---")
    col1, col2 = st.columns(2)

    with col1:
        if st.button("⬅️ Indietro", use_container_width=True):
            st.session_state["current_step"] = 7
            st.switch_page("pages/step7_idea_analyzer.py")

    with col2:
        if st.button("✅ Conferma e continua", type="primary", use_container_width=True):
            # Ensure data is saved before proceeding
            current_project_id = st.session_state.get("current_project_id")
            if current_project_id and not st.session_state.get("step8_saved"):
                step_data = {
                    "indice_libro_generato": st.session_state.get("indice_libro_generato", ""),
                    "indice_configurazione": st.session_state.get("indice_configurazione", {})
                }
                user_id = get_user_id()
                result = get_auth_manager().save_step_data(user_id, current_project_id, 8, step_data)
                if result.get("success"):
                    st.session_state["step8_saved"] = True

            st.session_state["current_step"] = 9
            st.switch_page("pages/step9_descrizione_amazon.py")






# Show help chat for step8
show_help_chat("step8_indice_perfetto")

import streamlit as st
from typing import Dict, List, Optional
import json
from datetime import datetime
from openai import OpenAI
from utils.config import config
from utils.auth import get_auth_manager

class HelpChatBot:
    """Floating help chat bot popup for KDP Genius platform"""

    def __init__(self):
        self.help_content = self._load_help_content()
        self.openai_client = None
        if config.OPENAI_API_KEY:
            self.openai_client = OpenAI(api_key=config.OPENAI_API_KEY)

    def _load_help_content(self) -> Dict[str, Dict]:
        """Load predefined help content for each page"""
        return {
            "step1_home": {
                "title": "🏠 Impostazioni Iniziali",
                "description": "Scegli il marketplace Amazon e la lingua target per il tuo libro.",
                "common_questions": [
                    ("Quale marketplace scegliere?", "Scegli il marketplace dove vuoi vendere. IT per Italia, US per Stati Uniti, UK per Regno Unito, etc."),
                    ("La lingua deve corrispondere al marketplace?", "Sì, generalmente è meglio che corrispondano. Esempio: IT = italiano, US = inglese."),
                    ("Posso cambiare marketplace dopo?", "No, ogni progetto di libro è legato al marketplace. Meglio scegliere bene dall'inizio!")
                ],
                "tips": [
                    "💡 Inizia con il tuo mercato locale se sei alle prime armi",
                    "💡 Il mercato US è il più grande ma anche il più competitivo",
                    "💡 Considera i mercati emergenti come DE (Germania) o ES (Spagna)"
                ]
            },
            "step2_argomento_keyword": {
                "title": "🔑 Argomento & Keyword Research",
                "description": "Analizza il mercato e scegli l'argomento perfetto per il tuo libro.",
                "common_questions": [
                    ("Come scelgo un buon argomento?", "Cerca un equilibrio tra passione personale e domanda di mercato. L'AI ti aiuterà analizzando i dati."),
                    ("Cosa fa l'analisi AI?", "Analizza trend, competitor, ricerche su Google/Amazon/Reddit, identifica pain points e ti dà insight sul linguaggio del target."),
                    ("Quanto deve essere specifico l'argomento?", "Meglio una nicchia specifica che un argomento troppo generico. Es: 'Giardinaggio urbano per principianti' invece di solo 'Giardinaggio'.")
                ],
                "tips": [
                    "💡 Usa parole chiave specifiche ma non troppo tecniche",
                    "💡 Controlla che ci sia domanda ma non troppa concorrenza",
                    "💡 L'AI ti mostrerà link reali a forum e discussioni"
                ]
            },
            "step3_recensioni": {
                "title": "📉 Analisi Recensioni",
                "description": "Scopri cosa NON piace ai lettori dei tuoi competitor per fare meglio!",
                "common_questions": [
                    ("Perché analizzare recensioni negative?", "Le recensioni negative ti dicono esattamente cosa evitare. È oro colato per creare un libro migliore!"),
                    ("Quante recensioni devo analizzare?", "L'AI analizzerà automaticamente le recensioni più rilevanti. Tu puoi anche aggiungerne manualmente."),
                    ("Cosa cercare nelle recensioni?", "Problemi ricorrenti, mancanze, aspettative deluse, errori tecnici.")
                ],
                "tips": [
                    "💡 Cerca pattern comuni nelle lamentele",
                    "💡 Prendi nota di cosa manca nei libri competitor",
                    "💡 Le recensioni 2-3 stelle sono spesso le più utili"
                ]
            },
            "step4_buyer_persona": {
                "title": "👤 Buyer Persona",
                "description": "Crea il profilo dettagliato del tuo lettore ideale.",
                "common_questions": [
                    ("Cos'è una buyer persona?", "È un profilo dettagliato e semi-fittizio del tuo cliente ideale, basato su dati reali."),
                    ("Perché è importante?", "Ti aiuta a scrivere per una persona specifica invece che per 'tutti'. Rende il contenuto più mirato ed efficace."),
                    ("Cosa include la buyer persona?", "Demografia, interessi, problemi, obiettivi, comportamenti d'acquisto, canali preferiti.")
                ]
            },
            "step5_posizionamento": {
                "title": "🎯 Posizionamento",
                "description": "Definisci come vuoi che il tuo libro sia percepito nel mercato.",
                "common_questions": [
                    ("Cos'è il posizionamento?", "È come vuoi che il tuo libro sia percepito: economico o premium? Per principianti o esperti? Pratico o teorico?"),
                    ("Come scelgo il posizionamento giusto?", "Basati sulla buyer persona e sull'analisi dei competitor. Cerca un gap nel mercato."),
                    ("Posso cambiare posizionamento dopo?", "È difficile. Meglio decidere bene ora perché influenzerà titolo, prezzo, contenuto.")
                ]
            },
            "step6_titolo_sottotitolo": {
                "title": "📝 Titolo & Sottotitolo",
                "description": "Crea titoli accattivanti che vendono!",
                "common_questions": [
                    ("Quanto è importante il titolo?", "FONDAMENTALE! Può fare la differenza tra successo e fallimento."),
                    ("Meglio creativo o descrittivo?", "Dipende dalla nicchia. I how-to preferiscono chiarezza, la narrativa può essere più creativa."),
                    ("Il sottotitolo è obbligatorio?", "Sì.")
                ]
            },
            "step7_idea_analyzer": {
                "title": "🔍 Analisi Idea",
                "description": "Verifica problemi di policy Amazon e parole bannate prima di procedere.",
                "common_questions": [
                    ("Cosa controlla l'Idea Analyzer?", "Verifica se ci sono problemi di copyright, marchi registrati, parole bannate da Amazon o contenuti sensibili."),
                    ("Quali parole sono bannate?", "Termini medici non verificati, promesse di guadagno irrealistiche, contenuti per adulti nascosti, marchi famosi."),
                    ("Cosa succede se trova problemi?", "Ti avvisa e suggerisce alternative sicure. Meglio saperlo ora che essere bannati dopo!")
                ]
            },
            "step8_indice_perfetto": {
                "title": "📚 Indice Strutturato",
                "description": "Crea la struttura perfetta per il tuo libro.",
                "common_questions": [
                    ("Quanti capitoli dovrebbe avere?", "Dipende dall'argomento, ma 8-15 è un buon range per la maggior parte dei libri."),
                    ("L'AI genera tutto l'indice?", "Sì, crea una struttura completa che puoi poi modificare secondo le tue esigenze."),
                    ("Devo seguire l'indice alla lettera?", "È una guida eccellente, ma sentiti libero di adattarlo mentre scrivi.")
                ]
            },
            "step9_descrizione_amazon": {
                "title": "🛍️ Descrizione Amazon",
                "description": "Scrivi una descrizione che converte visitatori in acquirenti.",
                "common_questions": [
                    ("Quanto deve essere lunga?", "Amazon permette fino a 4000 caratteri. Usa almeno 1000 per essere esaustivo."),
                    ("Cosa deve includere?", "Hook iniziale, benefici chiari, contenuto del libro, chiamata all'azione."),
                    ("Posso usare formattazione?", "Sì! Usa elenchi puntati, grassetto per i benefici chiave.")
                ]
            },
            "step10_cover_perfetta": {
                "title": "🎨 Cover Perfetta",
                "description": "Genera idee professionali per una copertina che attira l'attenzione.",
                "common_questions": [
                    ("Quanto è importante la cover?", "È la PRIMA cosa che vedono. Una brutta cover = nessuna vendita."),
                    ("Devo essere un designer?", "No! L'AI ti suggerisce idee e hai un editor integrato."),
                    ("Quali sono le dimensioni?", "Amazon richiede minimo 1000x1600 pixel, ideale 1600x2560.")
                ]
            },
            "step10b_design_cover": {
                "title": "🎨 Cover Design",
                "description": "Crea una copertina professionale che attira l'attenzione.",
                "common_questions": [
                    ("Posso saltare questo step senza problemi?", "Sì"),
                    ("Devo essere un designer?", "No! L'AI ti suggerisce idee e hai un editor integrato."),
                    ("Quali sono le dimensioni?", "Amazon richiede minimo 1000x1600 pixel, ideale 1600x2560.")
                ]
            },
            "vision_builder": {
                "title": "🎯 Vision Builder",
                "description": "Pianifica i tuoi obiettivi editoriali a lungo termine.",
                "common_questions": [
                    ("A cosa serve il Vision Builder?", "Ti aiuta a pianificare una strategia editoriale completa, non solo un singolo libro."),
                    ("Devo usarlo per forza?", "È opzionale ma MOLTO consigliato se vuoi fare sul serio con il self-publishing."),
                    ("Posso modificare la vision dopo?", "Certamente! È una guida flessibile che puoi adattare.")
                ]
            },
            "editorial_roadmap": {
                "title": "📚 Editorial Roadmap",
                "description": "Crea una strategia per serie di libri di successo.",
                "common_questions": [
                    ("Cos'è una serie editoriale?", "Un gruppo di libri correlati che si supportano a vicenda nelle vendite."),
                    ("Perché fare una serie?", "I lettori che amano un tuo libro probabilmente ne compreranno altri. Più libri = più entrate."),
                    ("Come scelgo i temi?", "L'AI analizza le sotto-nicchie e ti suggerisce l'ordine ottimale di pubblicazione.")
                ]
            },
            "dashboard": {
                "title": "📊 Dashboard",
                "description": "Il tuo centro di controllo KDP Genius.",
                "common_questions": [
                    ("Come inizio un nuovo progetto?", "Clicca su 'Nuovo Progetto' e segui il processo guidato in 12 step."),
                    ("Dove vedo i miei progetti?", "Nella sezione 'Projects' trovi tutti i tuoi libri in lavorazione e completati."),
                    ("Come acquisto crediti?", "Vai nella sezione 'Billing' per acquistare pacchetti di crediti.")
                ]
            }
        }

    def render_chat_widget(self, page_name: str):
        """Render the floating chat widget"""
        # Only show if page has help content
        if page_name not in self.help_content:
            return

        # Initialize chat state
        if "chat_open" not in st.session_state:
            st.session_state.chat_open = False
        if "chat_messages" not in st.session_state:
            st.session_state.chat_messages = []
        if "chat_mode" not in st.session_state:
            st.session_state.chat_mode = "faq"

        # Get page help content
        page_help = self.help_content.get(page_name, {})

        # Only show widget if there are FAQ questions
        if not page_help.get('common_questions'):
            return

        # Build FAQ HTML content
        faq_items = []
        for question, answer in page_help.get('common_questions', []):
            faq_items.append(f'<details style="margin-bottom: 10px;"><summary style="cursor: pointer; font-weight: bold; padding: 10px; background: #f5f5f5; border-radius: 8px;">{question}</summary><p style="padding: 10px; margin: 0;">{answer}</p></details>')
        faq_html = ''.join(faq_items)

        # Self-contained floating widget with CSS and JavaScript
        widget_html = f"""
        <style>
        /* Floating Action Button */
        .kdp-help-fab {{
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            background-color: #ff6b6b;
            color: white;
            border: none;
            border-radius: 50%;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px;
            transition: all 0.3s ease;
            z-index: 9999;
        }}

        .kdp-help-fab:hover {{
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(0,0,0,0.25);
        }}

        /* Chat Popup */
        .kdp-help-popup {{
            position: fixed;
            bottom: 90px;
            right: 20px;
            width: 380px;
            max-width: 90vw;
            height: 500px;
            max-height: 70vh;
            background: white;
            border-radius: 16px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.2);
            display: none;
            flex-direction: column;
            overflow: hidden;
            z-index: 9998;
        }}

        /* Show popup when targeted */
        #{page_name}_helpPopup:target {{
            display: flex;
        }}

        /* Tab switching */
        .kdp-help-popup:target ~ #helpPopup-faq:target #helpContent {{
            display: block;
        }}

        .kdp-help-popup:target ~ #helpPopup-chat:target #chatContent {{
            display: flex;
        }}

        .kdp-help-header {{
            background: linear-gradient(135deg, #ff6b6b 0%, #ff5252 100%);
            color: white;
            padding: 16px;
            border-radius: 16px 16px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }}

        .kdp-help-content {{
            padding: 20px;
            overflow-y: auto;
            flex: 1;
        }}

        @media (max-width: 600px) {{
            .kdp-help-popup {{
                right: 10px;
                left: 10px;
                width: auto;
                bottom: 80px;
                height: 60vh;
            }}
        }}
        </style>

        <a href="#{page_name}_helpPopup" class="kdp-help-fab" style="text-decoration: none; display: flex; align-items: center; justify-content: center;">💬</a>

        <div class="kdp-help-popup" id="{page_name}_helpPopup">
            <div class="kdp-help-header">
                <h3 style="margin: 0; color: white;">{page_help.get('title', 'Aiuto')}</h3>
                <a href="#close" style="background: none; border: none; color: white; font-size: 20px; cursor: pointer; text-decoration: none;">✕</a>
            </div>
            <div class="kdp-help-content">
                <p style="color: #666; margin-bottom: 20px;">{page_help.get('description', '')}</p>
                {faq_html}
            </div>
        </div>
        """

        # Render the floating widget
        st.markdown(widget_html, unsafe_allow_html=True)

    def _render_chat_interface(self, page_help: Dict):
        """Render the ChatGPT chat interface"""
        # Chat messages display
        messages_container = st.container()

        with messages_container:
            # Display chat history
            for msg in st.session_state.chat_messages:
                with st.chat_message(msg["role"]):
                    st.write(msg["content"])

        # Chat input
        if prompt := st.chat_input("Chiedi qualsiasi cosa..."):
            # Add user message
            st.session_state.chat_messages.append({"role": "user", "content": prompt})

            # Get AI response
            if self.openai_client:
                try:
                    # Prepare context
                    context = f"""
                    Sei un assistente per la piattaforma KDP Genius.
                    L'utente si trova nella sezione: {page_help.get('title', 'Sconosciuta')}
                    Descrizione: {page_help.get('description', '')}

                    Domande frequenti per questa sezione:
                    """

                    if page_help.get('common_questions'):
                        for q, a in page_help['common_questions']:
                            context += f"\nQ: {q}\nA: {a}\n"

                    # Create messages for API
                    messages = [
                        {"role": "system", "content": context},
                        *st.session_state.chat_messages
                    ]

                    # Get response
                    response = self.openai_client.chat.completions.create(
                        model="gpt-3.5-turbo",
                        messages=messages,
                        temperature=0.7,
                        max_tokens=500
                    )

                    # Add assistant response
                    assistant_msg = response.choices[0].message.content
                    st.session_state.chat_messages.append({"role": "assistant", "content": assistant_msg})

                except Exception as e:
                    st.error(f"Errore nella chat: {str(e)}")
                    st.session_state.chat_messages.append({
                        "role": "assistant",
                        "content": "Mi dispiace, c'è stato un errore. Per favore riprova."
                    })
            else:
                st.session_state.chat_messages.append({
                    "role": "assistant",
                    "content": "La chat AI non è configurata. Controlla la configurazione OpenAI."
                })

            st.rerun()


# Singleton instance
_help_chat = None

def get_help_chat() -> HelpChatBot:
    """Get or create the help chat singleton"""
    global _help_chat
    if _help_chat is None:
        _help_chat = HelpChatBot()
    return _help_chat

def show_help_chat(page_name: str):
    """Easy function to add floating help chat to any page

    Usage:
        from components.help_chat import show_help_chat
        show_help_chat("step1_home")

    Note: Only shows chat widget if help content exists for the page.
    """
    chat = get_help_chat()
    chat.render_chat_widget(page_name)

import streamlit as st
from utils.auth import get_auth_manager
import os
import re
st.markdown(
    """
    <style>
    /* Professional auth form styling */
    .stMainBlockContainer  {
        max-width: 480px !important;
        padding-top: 2rem !important;
        padding-left: 1rem !important;
        padding-right: 1rem !important;
    }


    /* Mobile styling */
    @media (max-width: 480px) {

        h1 {
            font-size: 2.2em !important;
        }

        h3 {
            font-size: 1.1em !important;
        }
    }

    /* Form elements styling */
    .stTextInput > div > div > input {
        font-size: 16px !important;
    }

    .stButton > button {
        width: 100% !important;
        font-weight: 500 !important;
    }
    </style>
    """,
    unsafe_allow_html=True,
)
# ──────


# Title
st.markdown(
    """
    <h1 style='text-align: center; color: #222; font-size: 3em;'>📝 Sign Up</h1>
    <h3 style='text-align: center; color: #444;'>Create your KDP GENIUS account</h3>
    """,
    unsafe_allow_html=True,
)

# Check if user is already authenticated (try Supabase Auth first)
if not st.session_state.get("authenticated", False):
    get_auth_manager().restore_session_from_supabase()

if st.session_state.get("authenticated", False):
    st.success("Sei già loggato")
    user_data = st.session_state.get("user_data", {})
    st.info(f"Welcome, {user_data.get('name', 'User')}!")

    col1, col2 = st.columns(2)
    with col1:
        if st.button("📊 Vai alla Dashboard", type="primary"):
            st.switch_page("pages/dashboard/overview.py")
            st.rerun()
    with col2:
        if st.button("📚 Inizia un nuovo libro"):
            st.switch_page("pages/dashboard/projects.py")
            st.rerun()
    st.stop()

def validate_email(email):
    """Validate email format"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_password(password):
    """Validate password strength"""
    if len(password) < 8:
        return False, "La Password deve essere lunga almeno 8 caratteri"
    if not re.search(r'[A-Za-z]', password):
        return False, "La Password deve contenere almeno una lettera"
    if not re.search(r'\d', password):
        return False, "La Password deve contenere almeno un numero"
    return True, "La Password è valida"

# Signup form
with st.container():

    with st.form("signup_form"):
        name = st.text_input("Il tuo nome e cognome", placeholder="Nome e Cognome")
        email = st.text_input("Email", placeholder="<EMAIL>")
        password = st.text_input("Password", type="password", placeholder="Create a strong password")
        confirm_password = st.text_input("Confirma la Password", type="password", placeholder="Conferma la tua password")

        # Terms and conditions
        terms_accepted = st.checkbox("Accetto i termini del servizio e della privacy")

        # Newsletter subscription
        newsletter = st.checkbox("Iscriviti alla newsletter di KDP GENIUS per suggerimenti e aggiornamenti", value=True)

        col1, col2 = st.columns([1, 1])
        with col1:
            signup_button = st.form_submit_button("🚀 Crea un account", type="primary", use_container_width=True)
        with col2:
            clear_button = st.form_submit_button("🔄 Resetta il form", use_container_width=True)

    # Handle form submission
    if signup_button:
        # Validation
        errors = []

        if not name.strip():
            errors.append("Il nome è obbligatorio")

        if not email:
            errors.append("Email è obbligatoria")
        elif not validate_email(email):
            errors.append("Inserisci un indirizzo email valido")

        if not password:
            errors.append("Password è obbligatoria")
        else:
            is_valid, message = validate_password(password)
            if not is_valid:
                errors.append(message)

        if password != confirm_password:
            errors.append("Le password non corrispondono")

        if not terms_accepted:
            errors.append("Devi accettare i Termini di Servizio e la Politica sulla Privacy")

        # Display errors
        if errors:
            for error in errors:
                st.error(error)
        else:
            # Attempt to create account
            with st.spinner("Creo il tuo account..."):
                result = get_auth_manager().signup_user(email, password, name.strip())

                if result["success"]:
                    # Session state is already set by signup_user method
                    st.success("Account creato e accesso effettuato con successo!")
                    st.balloons()

                    # New users go directly to step 1 of book creation
                    st.info("Benvenuto in KDP GENIUS! Creiamo insieme il tuo primo progetto di libro.")
                    st.switch_page("pages/step1_home.py")
                else:
                    st.error(result["message"])

    # Handle clear form
    if clear_button:
        st.rerun()

# Password requirements info
st.markdown("""📋 La Password deve essere:
- Lunga almeno 8 caratteri
- Contenere almeno una lettera (A-Z or a-z)
- Contanere almeno un numero (0-9)
- Caratteri speciali sono opzionali ma raccomandati
""")

# Navigation links
st.markdown("---")
col1, col2 = st.columns(2)

with col1:
    st.markdown("**Hai già un account?**")
    if st.button("🔐 Accedi", use_container_width=True):
        st.switch_page("pages/auth_login.py")

with col2:
    st.markdown("**Bisogno di aiuto?**")
    if st.button("❓ Supporto", use_container_width=True):
        st.info("Contatta il nostro supporto: <EMAIL>")



# Footer
st.markdown("---")
st.markdown(
    """
    <div style="text-align: center; color: #666; font-size: 0.8em;">
        <p>© 2024 KDP GENIUS - Your intelligent publishing ally</p>
        <p><a href="#" style="color: #666;">Terms of Service</a> | <a href="#" style="color: #666;">Privacy Policy</a></p>
    </div>
    """,
    unsafe_allow_html=True,
)

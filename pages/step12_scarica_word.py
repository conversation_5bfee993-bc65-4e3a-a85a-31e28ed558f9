import streamlit as st
import datetime
import re
from docx import Document
from docx.shared import Pt, RGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH
from io import BytesIO
from docx.oxml.ns import qn
from utils.auth import get_auth_manager
from utils.credits import get_user_credits, get_user_id
from components.help_chat import show_help_chat
import requests
from docx.shared import Inches
import uuid
import gc
from utils.memory_optimization import cleanup_large_objects

# Require authentication
get_auth_manager().require_auth()



def markdown_to_docx(doc, markdown_text):
    """Convert markdown text to formatted docx paragraphs."""
    if not markdown_text:
        return

    # Clean up code block markers
    markdown_text = re.sub(
        r"```\w*\n", "", markdown_text
    )  # Remove opening code block markers
    markdown_text = re.sub(
        r"```", "", markdown_text
    )  # Remove closing code block markers

    # Split by lines for processing
    lines = markdown_text.split("\n")
    current_list = None
    list_level = 0
    in_code_block = False

    i = 0
    while i < len(lines):
        line = lines[i].rstrip()

        # Skip empty lines
        if not line.strip():
            i += 1
            continue

        # Headers
        header_match = re.match(r"^(#{1,6})\s+(.+)$", line)
        if header_match:
            level = len(header_match.group(1))
            text = header_match.group(2).strip()
            doc.add_heading(text, level=level)
            i += 1
            continue

        # Bullet lists (including •, *, -, +)
        list_match = re.match(r"^(\s*)([•*\-+])\s+(.+)$", line)
        if list_match:
            indent = len(list_match.group(1))
            text = list_match.group(3).strip()

            # Process formatting in list items
            p = doc.add_paragraph(style="List Bullet")

            # Handle bold and italic in list items
            process_formatting(p, text)
            i += 1
            continue

        # Numbered lists
        num_list_match = re.match(r"^(\s*)\d+\.\s+(.+)$", line)
        if num_list_match:
            indent = len(num_list_match.group(1))
            text = num_list_match.group(2).strip()
            p = doc.add_paragraph(style="List Number")

            # Handle formatting in numbered list items
            process_formatting(p, text)
            i += 1
            continue

        # Regular paragraph
        p = doc.add_paragraph()
        process_formatting(p, line)
        i += 1


def process_formatting(paragraph, text):
    """Process bold and italic formatting within a text and add to paragraph."""
    # Handle bold
    bold_segments = re.findall(r"\*\*(.+?)\*\*", text)
    for bold_text in bold_segments:
        text = text.replace(f"**{bold_text}**", f"BOLD_START{bold_text}BOLD_END")

    # Handle italic
    italic_segments = re.findall(r"\*(.+?)\*", text)
    for italic_text in italic_segments:
        if f"**{italic_text}**" not in text:  # Avoid matching inside bold text
            text = text.replace(
                f"*{italic_text}*", f"ITALIC_START{italic_text}ITALIC_END"
            )

    # Split by formatting markers and add runs with appropriate formatting
    parts = re.split(r"(BOLD_START|BOLD_END|ITALIC_START|ITALIC_END)", text)

    bold_on = False
    italic_on = False

    for part in parts:
        if part == "BOLD_START":
            bold_on = True
        elif part == "BOLD_END":
            bold_on = False
        elif part == "ITALIC_START":
            italic_on = True
        elif part == "ITALIC_END":
            italic_on = False
        elif part:  # Skip empty parts
            run = paragraph.add_run(part)
            run.bold = bold_on
            run.italic = italic_on


def aggiungi_sezione(doc, titolo, contenuto, level=1):
    """Add a section to the document with proper markdown conversion."""
    doc.add_heading(titolo, level=level)
    if contenuto and str(contenuto).strip().lower() not in ["none", "non disponibile"]:
        # Convert markdown to docx
        markdown_to_docx(doc, str(contenuto))
    else:
        p = doc.add_paragraph("Dato non disponibile")
        p.style = "Intense Quote"



# Progress bar function
def show_progress():
    """Display progress bar"""
    st.progress(
        min(12 / 12, 1.0), text=f"{int(12 / 12 * 100)}% completato"
    )

show_progress()

st.markdown(
    """
    <h2 style="text-align: center;">Step 12: Esporta il Progetto Editoriale</h2>
    <p style="text-align: center;">Scarica il tuo riepilogo completo in formato Word.</p>

""",
    unsafe_allow_html=True,
)
st.markdown("<div>&nbsp;</div>", unsafe_allow_html=True)
st.markdown("<div>&nbsp;</div>", unsafe_allow_html=True)
if st.button("Genera Documento Word", use_container_width=True,type="primary"):
    with st.spinner("Creazione documento in corso..."):
        try:
            doc = Document()

            # Add project header if available
            current_project_name = st.session_state.get("current_project_name", "")
            current_book_topic = st.session_state.get("current_book_topic", "")
            if current_project_name and current_book_topic:
                doc.add_heading(f"📦 {current_project_name}", 0)
                doc.add_heading(f"Libro: {current_book_topic}", 1)
            else:
                doc.add_heading("📦 Riepilogo Progetto Editoriale", 0)

            sezioni = [
                ("1. 🔍 Analisi Argomento / Keyword", "argomento_keyword"),
                ("2. ✍️ Recensioni Analizzate", None),
                ("3. 👤 Buyer Persona Generata", "buyer_persona_generata"),
                ("4. 🎯 Posizionamento Editoriale", "posizionamento_editoriale"),
                ("5. 🏷️ Titolo e Sottotitolo", None),
                ("6. 📘 Indice del Libro Generato", "indice_libro_generato"),
                ("7. 📝 Descrizione Amazon", "descrizione_amazon"),
                ("8. 🧠 Analisi Critica delle Recensioni", "report_recensioni"),
                ("9. 🖼️ Consigli Cover", "cover_suggerita"),
                ("10. 🎨 Cover Design", None),
            ]

            for titolo, key in sezioni:
                if key:
                    aggiungi_sezione(doc, titolo, st.session_state.get(key))
                elif "Recensioni Analizzate" in titolo:
                    # Handle recensioni section with both report and raw data
                    report = st.session_state.get("report_recensioni", "")
                    raw_recensioni = st.session_state.get("recensioni_inserite", [])

                    if isinstance(raw_recensioni, list):
                        raw_text = "\n\n".join(raw_recensioni)
                    else:
                        raw_text = str(raw_recensioni)

                    contenuto_completo = ""
                    if report:
                        contenuto_completo += f"# Analisi delle Recensioni\n\n{report}\n\n"
                    if raw_text:
                        contenuto_completo += f"# Recensioni Raw\n\n{raw_text}"

                    if not contenuto_completo:
                        contenuto_completo = "Dato non disponibile"

                    aggiungi_sezione(doc, titolo, contenuto_completo)
                elif "Titolo e Sottotitolo" in titolo:
                    titolo_val = st.session_state.get("titolo_scelto", "")
                    sottotitolo_val = st.session_state.get("sottotitolo_scelto", "")
                    contenuto = (
                        f"Titolo: {titolo_val}\nSottotitolo: {sottotitolo_val}"
                        if titolo_val and sottotitolo_val
                        else "Dato non disponibile"
                    )
                    aggiungi_sezione(doc, titolo, contenuto)

                elif "Indice del Libro Generato" in titolo:
                    doc.add_heading(titolo, level=1)
                    aggiungi_sezione(doc, titolo, st.session_state.get("indice_libro_generato"))
                elif "Cover Design" in titolo:
                    # Handle cover image and suggestions
                    doc.add_heading(titolo, level=1)

                    # Function to get cover URL from various sources
                    def get_cover_url_comprehensive():
                        # List of all possible keys to check
                        url_keys = ["cover_url", "cover_image_url", "generated_cover_url", "final_cover_url"]

                        # First try direct session state with all possible keys
                        for key in url_keys:
                            url = st.session_state.get(key, "")
                            if url:
                                return url

                        # Try step_data (loaded from projects.py)
                        step_data = st.session_state.get("step_data", {})

                        # Check step10b data with all possible keys
                        step10b = step_data.get("step10b", {})
                        for key in url_keys:
                            url = step10b.get(key, "")
                            if url:
                                return url

                        # Check step10 data (sometimes cover URL might be here)
                        step10 = step_data.get("step10", {})
                        for key in url_keys:
                            url = step10.get(key, "")
                            if url:
                                return url

                        # Check step11 data
                        step11 = step_data.get("step11", {})
                        summary_data = step11.get("summary_data", {})
                        for key in url_keys:
                            url = summary_data.get(key, "")
                            if url:
                                return url

                        # Try project data at root level
                        project_data = st.session_state.get("project_data", {})
                        for key in url_keys:
                            url = project_data.get(key, "")
                            if url:
                                return url

                        # Try final result
                        final_result = st.session_state.get("final_result", {})
                        if isinstance(final_result, str):
                            try:
                                import json
                                final_result = json.loads(final_result)
                            except:
                                final_result = {}
                        if isinstance(final_result, dict):
                            for key in url_keys:
                                url = final_result.get(key, "")
                                if url:
                                    return url

                        # Check current project from database (fallback)
                        current_project = st.session_state.get("current_project", {})
                        for key in url_keys:
                            url = current_project.get(key, "")
                            if url:
                                return url

                        return ""

                    # Function to get cover suggestions from various sources
                    def get_cover_suggestions_comprehensive():
                        # List of all possible keys for cover suggestions
                        suggestion_keys = ["cover_suggerita", "cover_suggestions", "cover_suggestion", "cover_report", "cover_ideas"]

                        # First try direct session state with all possible keys
                        for key in suggestion_keys:
                            suggestions = st.session_state.get(key, "")
                            if suggestions:
                                return suggestions

                        # Try step_data (loaded from projects.py)
                        step_data = st.session_state.get("step_data", {})

                        # Check step10 data with all possible keys
                        step10 = step_data.get("step10", {})
                        for key in suggestion_keys:
                            suggestions = step10.get(key, "")
                            if suggestions:
                                return suggestions

                        # Also check for selected_cover_idea in step10
                        selected_idea = step10.get("selected_cover_idea", "")
                        if selected_idea:
                            return selected_idea

                        # Check step10b data with all possible keys
                        step10b = step_data.get("step10b", {})
                        for key in suggestion_keys:
                            suggestions = step10b.get(key, "")
                            if suggestions:
                                return suggestions

                        # Also check for selected_cover_idea in step10b
                        selected_idea = step10b.get("selected_cover_idea", "")
                        if selected_idea:
                            return selected_idea

                        # Check step11 data
                        step11 = step_data.get("step11", {})
                        summary_data = step11.get("summary_data", {})
                        for key in suggestion_keys:
                            suggestions = summary_data.get(key, "")
                            if suggestions:
                                return suggestions

                        # Try summary_data directly in session state
                        summary = st.session_state.get("summary_data", {})
                        for key in suggestion_keys:
                            suggestions = summary.get(key, "")
                            if suggestions:
                                return suggestions

                        # Try project data at root level
                        project_data = st.session_state.get("project_data", {})
                        for key in suggestion_keys:
                            suggestions = project_data.get(key, "")
                            if suggestions:
                                return suggestions

                        # Try final result
                        final = st.session_state.get("final_result", {})
                        if isinstance(final, str):
                            try:
                                import json
                                final = json.loads(final)
                            except:
                                final = {}
                        if isinstance(final, dict):
                            for key in suggestion_keys:
                                suggestions = final.get(key, "")
                                if suggestions:
                                    return suggestions

                        # Check current project from database (fallback)
                        current_project = st.session_state.get("current_project", {})
                        for key in suggestion_keys:
                            suggestions = current_project.get(key, "")
                            if suggestions:
                                return suggestions

                        return ""

                    # Check if cover was skipped
                    cover_skipped = st.session_state.get("cover_skipped", False)

                    # Get cover data using comprehensive search
                    cover_suggestions = get_cover_suggestions_comprehensive()
                    cover_url = get_cover_url_comprehensive()
                    # st.write(f"🔍 DEBUG: Cover suggestions length: {len(cover_suggestions) if cover_suggestions else 0}")
                    # st.write(f"🔍 DEBUG: Cover URL: {cover_url[:50] if cover_url else 'None'}...")

                    if cover_skipped:
                        doc.add_paragraph("ℹ️ Generazione cover saltata - Puoi aggiungere la cover in seguito")
                    else:
                        # Add cover suggestions from step 10 first
                        if cover_suggestions:
                            doc.add_heading("Suggerimenti Cover dall'AI", level=2)
                            markdown_to_docx(doc, cover_suggestions)
                        else:
                            doc.add_paragraph("Suggerimenti cover non disponibili")

                        # Add cover image
                        if cover_url:
                            doc.add_heading("Cover Generata", level=2)
                        try:
                            if cover_url.startswith("data:image"):
                                # Handle data URL
                                import base64
                                header, data = cover_url.split(',', 1)
                                image_data = base64.b64decode(data)
                                image_stream = BytesIO(image_data)
                                doc.add_picture(image_stream, width=Inches(4))
                            else:
                                # Download from URL
                                response = requests.get(cover_url)
                                if response.status_code == 200:
                                    image_stream = BytesIO(response.content)
                                    doc.add_picture(image_stream, width=Inches(4))

                                else:
                                    doc.add_paragraph("Cover non disponibile")
                        except Exception as e:

                            doc.add_paragraph(f"Errore caricamento cover: {str(e)}")
                        else:
                            doc.add_paragraph("Cover non generata")

            buffer = BytesIO()
            doc.save(buffer)
            buffer.seek(0)

            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"Riepilogo_Progetto_{timestamp}.docx"

            # Upload to Supabase
            auth_mgr = get_auth_manager()
            if hasattr(auth_mgr, 'conn') and auth_mgr.conn:
                try:
                    # Create unique filename
                    unique_filename = f"{uuid.uuid4()}_{filename}"

                    # Upload to covers bucket
                    result = auth_mgr.conn.storage.from_("covers").upload(
                        path=unique_filename,
                        file=buffer.getvalue(),
                        file_options={"content-type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document"}
                    )

                    # Get public URL
                    public_url = auth_mgr.conn.storage.from_("covers").get_public_url(unique_filename)

                    st.success("✅ Documento Word generato e caricato!")
                    st.markdown(f"📥 [Scarica il documento]({public_url})")

                    # Save URL to session state
                    st.session_state["document_url"] = public_url
                except Exception as e:
                    st.error(f"Errore upload documento: {str(e)}")
                    # Fallback to download button
                    buffer.seek(0)
                    st.download_button(
                        label="📥 Scarica Riepilogo Word (locale)",
                        data=buffer,
                        file_name=filename,
                        mime="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                    )
            else:
                # Fallback if no Supabase connection
                st.download_button(
                    label="📥 Scarica Riepilogo Word",
                    data=buffer,
                    file_name=filename,
                    mime="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                )

            # Save final results to project if exists
            if current_project_name:
                try:
                    user_id = get_user_id()
                    projects = get_auth_manager().get_user_projects(user_id)
                    current_project = None
                    if projects:
                        for project in projects:
                            if project.get("project_name") == st.session_state.get("current_project_name"):
                                current_project = project
                                break

                    if current_project:
                        # Update project with completion
                        project_data = current_project.get("project_data", {})
                        project_data["step_data"]["step12"] = {
                            "completed": True,
                            "timestamp": datetime.datetime.now().isoformat(),
                            "document_filename": filename,
                            "document_url": st.session_state.get("document_url", "")
                        }
                        project_data["current_step"] = 12

                        # Update books status in roadmap
                        if "books_status" in project_data:
                            current_book_index = project_data.get("current_book_index", 0)
                            if current_book_index < len(project_data["books_status"]):
                                project_data["books_status"][current_book_index]["status"] = "completed"
                                project_data["books_status"][current_book_index]["completed_steps"] = list(range(1, 13))
                                project_data["books_status"][current_book_index]["document_url"] = st.session_state.get("document_url", "")

                        get_auth_manager().update_project(current_project["id"], {
                            "project_data": project_data,
                            "status": "completed"
                        })

                        st.info("✅ Progetto aggiornato con i risultati finali!")
                except Exception as e:
                    st.warning(f"Avviso: Impossibile aggiornare il progetto: {str(e)}")

            # Clean up the document buffer after upload
            cleanup_large_objects(buffer, doc)
            gc.collect()

        except Exception as e:
            st.error(f"❌ Errore durante la generazione: {str(e)}")
            if "docx" in str(e):
                st.info(
                    "💡 Assicurati di avere installato `python-docx` (`pip install python-docx`)"
                )


# Add PlottyBot button if document has been generated
if st.session_state.get("document_url"):
    st.markdown("---")
    if st.button("🤖 Scrivilo con PlottyBot", type="primary", use_container_width=True):
        plottybot_url = "https://plottybot.com/?link=161"
        st.markdown(f'<meta http-equiv="refresh" content="0; url={plottybot_url}">', unsafe_allow_html=True)
        st.success("✅ Apertura PlottyBot...")

st.markdown("---")
col1, col2= st.columns([1, 1])
with col1:
    if st.button("⬅️ Indietro", use_container_width=True):
        st.session_state["current_step"] = 11
        st.switch_page("pages/step11_riepilogo.py")

with col2:
    if st.session_state.get("current_project_name") and st.button("📚 Prossimo Libro", use_container_width=True):
        # Check if there are more books in the roadmap
        user_id = get_user_id()
        projects = get_auth_manager().get_user_projects(user_id)
        current_project = None
        if projects:
            for project in projects:
                if project.get("project_name") == st.session_state.get("current_project_name"):
                    current_project = project
                    break

        if current_project:
            project_data = current_project.get("project_data", {})
            current_book_index = project_data.get("current_book_index", 0)
            books_status = project_data.get("books_status", [])

            if current_book_index + 1 < len(books_status):
                # Move to next book
                next_book_index = current_book_index + 1
                next_book = books_status[next_book_index]

                # Update project
                project_data["current_book_index"] = next_book_index
                project_data["current_step"] = 1
                project_data["books_status"][next_book_index]["status"] = "in_progress"

                get_auth_manager().update_project(current_project["id"], {
                    "project_data": project_data,
                    "status": "in_progress",
                    "progress_percentage": int((next_book_index / len(books_status)) * 100)
                })

                # Update session state
                st.session_state["current_book_topic"] = next_book["topic"]
                st.session_state["current_step"] = 1

                # Clear previous book data
                for key in ["argomento_keyword", "buyer_persona_generata", "titolo_scelto",
                           "sottotitolo_scelto", "indice_libro_generato", "descrizione_amazon",
                           "cover_suggerita", "posizionamento_editoriale", "recensioni_inserite",
                           "report_recensioni", "idea_analysis"]:
                    if key in st.session_state:
                        del st.session_state[key]

                st.success(f"🎉 Iniziando il prossimo libro: {next_book['topic']}")
                st.switch_page("pages/step1_home.py")
            else:
                st.info("🎉 Hai completato tutti i libri della serie!")


st.markdown(
    "<p style='text-align: center;'>🎉 <b>Fine del percorso strategico!</b> Puoi ora esportare e pubblicare con chiarezza.</p>",
    unsafe_allow_html=True,
)

# Show help chat for step12
show_help_chat("step12_scarica_word")

import openai
from typing import Dict, Any, List
import json


def genera_indice(
    dati: Dict[str, Any],
    stile: str,
    openai_key: str,
    prompt_custom: str,
    lingua: str = "italiano",
) -> str:
    """
    Genera un indice intelligente per libri KDP non-fiction.
    
    Args:
        dati: Dizionario con i dati del libro (argomento, target, etc.)
        stile: Tipologia del libro
        openai_key: Chiave API OpenAI
        prompt_custom: Prompt personalizzato per la generazione
        lingua: Lingua di output
        
    Returns:
        str: Indice generato in formato Markdown
        
    Raises:
        ValueError: Se la chiave OpenAI è mancante
        Exception: Per errori durante la generazione
    """
    if not openai_key:
        raise ValueError("❌ Chiave OpenAI mancante.")
    
    try:
        client = openai.OpenAI(api_key=openai_key)
        
        # Sistema prompt specializzato per indici KDP
        system_prompt = f"""
        Sei un editor senior specializzato in Amazon KDP con 15+ anni di esperienza nella creazione di libri non-fiction bestseller.
        
        EXPERTISE:
        - Strutturazione ottimale di indici per massimizzare vendite KDP
        - Organizzazione logica e progressiva dei contenuti
        - Bilanciamento perfetto tra teoria e pratica
        - Ottimizzazione per diversi target di pubblico
        - Numerazione gerarchica professionale
        
        REGOLE FONDAMENTALI:
        1. Genera SEMPRE indici in {lingua}
        2. Usa numerazione gerarchica standard (1., 1.1, 1.1.1)
        3. Mantieni coerenza tematica per capitolo
        4. Progressione logica: base → intermedio → avanzato
        5. Ogni livello deve aggiungere valore specifico
        6. Titoli chiari, concreti e orientati al risultato
        7. Equilibrio tra sezioni teoriche e pratiche
        
        FORMATO OUTPUT:
        - Markdown pulito e professionale
        - Gerarchia visiva chiara
        - Numerazione consistente
        - Struttura facilmente navigabile
        
        SPECIALIZZAZIONI PER TIPO:
        - Study Guide: Focus su apprendimento progressivo + capitolo soluzioni
        - Ricettari: Organizzazione per difficoltà/categoria/stagionalità
        - Manuali: Struttura logica passo-passo
        - Guide: Orientamento pratico e applicativo
        """
        
        # Messaggio user arricchito con dati contestuali
        user_message = f"""
        {prompt_custom}
        
        CONTESTO AGGIUNTIVO:
        - Libro destinato a Amazon KDP
        - Target commerciale: massimizzare appeal e usabilità
        - Struttura deve essere immediatamente comprensibile
        - Bilanciamento contenuti per ottimizzare tempo lettura
        
        DATI LIBRO:
        {json.dumps(dati, indent=2, ensure_ascii=False)}
        
        ISTRUZIONI SPECIFICHE:
        1. Crea una struttura che faciliti la navigazione del lettore
        2. Ogni capitolo deve avere un obiettivo chiaro e raggiungibile
        3. I sotto-capitoli devono essere logicamente collegati
        4. La numerazione deve essere professionale e consistente
        5. I titoli devono essere accattivanti ma informativi
        6. Include elementi pratici che aumentino il valore percepito
        
        Genera un indice che un lettore guarderebbe e penserebbe: "Questo libro risolve esattamente i miei problemi!"
        """
        
        messages: List[Dict[str, str]] = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_message},
        ]
        
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=messages,  # type: ignore
            temperature=0.7,
            max_tokens=3000,
            top_p=0.9,
            frequency_penalty=0.1,
            presence_penalty=0.1
        )
        
        indice_generato = response.choices[0].message.content
        
        if not indice_generato:
            raise Exception("Risposta vuota dall'API OpenAI")
            
        # Post-processing per garantire qualità
        indice_processato = post_process_indice(indice_generato, dati, stile)
        
        return indice_processato
        
    except openai.RateLimitError as e:
        raise Exception(f"❗ Limite rate OpenAI raggiunto: {str(e)}")
    except openai.AuthenticationError as e:
        raise Exception(f"❗ Errore autenticazione OpenAI: {str(e)}")
    except openai.APIError as e:
        raise Exception(f"❗ Errore API OpenAI: {str(e)}")
    except Exception as e:
        raise Exception(f"❗ Errore durante la generazione dell'indice: {str(e)}")


def post_process_indice(indice: str, dati: Dict[str, Any], stile: str) -> str:
    """
    Post-processa l'indice generato per garantire qualità e coerenza.
    
    Args:
        indice: Indice generato dall'AI
        dati: Dati del libro
        stile: Tipologia del libro
        
    Returns:
        str: Indice processato e ottimizzato
    """
    # Rimuovi eventuali artefatti comuni
    indice_pulito = indice.strip()
    
    # Assicurati che inizi con un header se necessario
    if not indice_pulito.startswith('#') and not indice_pulito.startswith('**'):
        indice_pulito = f"# INDICE\n\n{indice_pulito}"
    
    # Validazioni specifiche per tipo di libro
    if stile in ["Study Guide", "Guida all'Esame"]:
        if "soluzioni" not in indice_pulito.lower() and "risposte" not in indice_pulito.lower():
            indice_pulito += "\n\n## Capitolo Finale: Risposte e Soluzioni\nTutte le soluzioni dettagliate e spiegate degli esercizi del libro."
    
    # Aggiungi metadati se necessario
    if "NumeroContenuti" in dati and "CategoriaContenuto" in dati:
        numero = dati["NumeroContenuti"]
        categoria = dati["CategoriaContenuto"]
        
        # Aggiungi nota informativa all'inizio se non presente
        if f"{numero}" not in indice_pulito and f"{categoria}" not in indice_pulito:
            nota = f"\n*Questo libro contiene {numero} {categoria.lower()} pratici organizzati in modo progressivo e logico.*\n"
            # Inserisci dopo il primo header
            lines = indice_pulito.split('\n')
            for i, line in enumerate(lines):
                if line.startswith('#'):
                    lines.insert(i + 1, nota)
                    break
            indice_pulito = '\n'.join(lines)
    
    return indice_pulito


def valida_struttura_indice(indice: str) -> Dict[str, Any]:
    """
    Valida la struttura dell'indice generato.
    
    Args:
        indice: Indice da validare
        
    Returns:
        dict: Risultato validazione con score e suggerimenti
    """
    score = 0
    suggerimenti = []
    
    lines = indice.split('\n')
    
    # Verifica presenza capitoli numerati
    capitoli_numerati = sum(1 for line in lines if line.strip().startswith('# Capitolo') or 'Capitolo' in line)
    if capitoli_numerati >= 3:
        score += 20
    else:
        suggerimenti.append("Aggiungi più capitoli numerati (minimo 3)")
    
    # Verifica sotto-capitoli
    sotto_capitoli = sum(1 for line in lines if line.strip().startswith('##') or '1.' in line or '2.' in line)
    if sotto_capitoli >= 5:
        score += 20
    else:
        suggerimenti.append("Aggiungi più sotto-capitoli per dettaglio")
    
    # Verifica lunghezza appropriata
    if 20 <= len(lines) <= 100:
        score += 20
    else:
        suggerimenti.append("Lunghezza indice non ottimale (20-100 righe)")
    
    # Verifica presenza introduzione
    if any('introduzione' in line.lower() or 'premessa' in line.lower() for line in lines):
        score += 20
    else:
        suggerimenti.append("Considera di aggiungere una sezione introduttiva")
    
    # Verifica coerenza numerazione
    numerazioni = [line for line in lines if any(char.isdigit() for char in line[:10])]
    if len(numerazioni) >= 3:
        score += 20
    else:
        suggerimenti.append("Migliora la numerazione gerarchica")
    
    return {
        "score": score,
        "livello": "Eccellente" if score >= 80 else "Buono" if score >= 60 else "Migliorabile",
        "suggerimenti": suggerimenti
    }
-- Create credits_usage table for tracking credit consumption
CREATE TABLE IF NOT EXISTS public.credits_usage (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    action_type TEXT NOT NULL CHECK (action_type IN ('keyword_analysis', 'review_analysis', 'content_generation', 'cover_generation', 'other')),
    credits_used INTEGER NOT NULL DEFAULT 1,
    project_id INTEGER REFERENCES public.projects(id) ON DELETE SET NULL,
    description TEXT,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for faster queries
CREATE INDEX IF NOT EXISTS idx_credits_usage_user_id ON public.credits_usage(user_id);
CREATE INDEX IF NOT EXISTS idx_credits_usage_action_type ON public.credits_usage(action_type);
CREATE INDEX IF NOT EXISTS idx_credits_usage_project_id ON public.credits_usage(project_id);
CREATE INDEX IF NOT EXISTS idx_credits_usage_created_at ON public.credits_usage(created_at);

-- Enable Row Level Security
ALTER TABLE public.credits_usage ENABLE ROW LEVEL SECURITY;

-- Create policy for users to only see their own usage
CREATE POLICY "Users can view their own credits usage" ON public.credits_usage
    FOR SELECT USING (auth.uid() = user_id);

-- Create policy for inserting usage records (service role only)
CREATE POLICY "Service role can insert credits usage" ON public.credits_usage
    FOR INSERT WITH CHECK (true);

-- Create a function to automatically deduct credits and log usage
CREATE OR REPLACE FUNCTION deduct_user_credits(
    p_user_id UUID,
    p_action_type TEXT,
    p_credits_amount INTEGER DEFAULT 1,
    p_project_id INTEGER DEFAULT NULL,
    p_description TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    current_credits INTEGER;
BEGIN
    -- Get current user credits
    SELECT credits INTO current_credits 
    FROM public.users 
    WHERE id = p_user_id;
    
    -- Check if user has enough credits
    IF current_credits < p_credits_amount THEN
        RETURN FALSE;
    END IF;
    
    -- Deduct credits from user account
    UPDATE public.users 
    SET credits = credits - p_credits_amount,
        updated_at = NOW()
    WHERE id = p_user_id;
    
    -- Log the credit usage
    INSERT INTO public.credits_usage (
        user_id, 
        action_type, 
        credits_used, 
        project_id, 
        description
    ) VALUES (
        p_user_id, 
        p_action_type, 
        p_credits_amount, 
        p_project_id, 
        p_description
    );
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to get user credit usage summary
CREATE OR REPLACE FUNCTION get_user_credits_summary(p_user_id UUID)
RETURNS TABLE (
    action_type TEXT,
    total_credits_used INTEGER,
    usage_count INTEGER,
    last_used TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        cu.action_type,
        SUM(cu.credits_used)::INTEGER as total_credits_used,
        COUNT(*)::INTEGER as usage_count,
        MAX(cu.created_at) as last_used
    FROM public.credits_usage cu
    WHERE cu.user_id = p_user_id
    GROUP BY cu.action_type
    ORDER BY total_credits_used DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT ALL ON public.credits_usage TO authenticated;
GRANT ALL ON public.credits_usage TO service_role;
GRANT EXECUTE ON FUNCTION deduct_user_credits TO service_role;
GRANT EXECUTE ON FUNCTION get_user_credits_summary TO authenticated;
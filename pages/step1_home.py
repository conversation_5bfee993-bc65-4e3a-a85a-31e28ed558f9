import streamlit as st
from PIL import Image
import os
from utils.auth import get_auth_manager
from utils.credits import get_user_id
from components.help_chat import show_help_chat
# Require authentication
get_auth_manager().require_auth()






# Debug info
def show_debug_info():
    """Show debug information in sidebar"""
    st.sidebar.write(f"Project ID: {st.session_state.get('current_project_id')}")
    st.sidebar.write(f"Project Name: {st.session_state.get('current_project_name', 'None')}")
    st.sidebar.write(f"Marketplace: {st.session_state.get('marketplace', 'IT')}")
    st.sidebar.write(f"Language: {st.session_state.get('lingua_target', 'it')}")


#activate to debug the project info
# show_debug_info()

def clear_all_project_data():
    """Clear all project-related data from session state when starting a new project"""
    # List of all project-related keys from step11_riepilogo.py
    project_keys = [
        # Step 1
        "current_project_id",
        "current_project_name",
        "current_book_topic",
        "marketplace",
        "lingua_target",
        "current_step",
        "last_step_completed",

        # Step 2
        "argomento_keyword",
        "analisi_argomento_generata",
        "amazon_books_data",
        "amazon_books",

        # Step 3
        "recensioni_analizzate",
        "report_recensioni",
        "recensioni_inserite",
        "problemi_recensioni",

        # Step 4
        "buyer_persona_generata",

        # Step 5
        "posizionamento_editoriale",

        # Step 6
        "titolo_scelto",
        "sottotitolo_scelto",
        "titoli_sottotitoli_generati",

        # Step 7
        "idea_analysis",
        "pain_points",
        "jobs_to_be_done",

        # Step 8
        "indice_libro_generato",

        # Step 9
        "descrizione_amazon",

        # Step 10/10b
        "cover_suggerita",
        "cover_suggestions",
        "cover_suggestion",
        "cover_report",
        "cover_ideas",
        "selected_cover_idea",
        "cover_url",
        "cover_image_url",
        "generated_cover_url",
        "final_cover_url",
        "cover_skipped",

        # Step 11
        "riepilogo_completed",
        "summary_data",

        # Other related keys
        "step_data",
        "project_data",
        "final_result",
        "current_project",

        # Step saved flags
        "step1_saved",
        "step2_saved",
        "step3_saved",
        "step4_saved",
        "step5_saved",
        "step6_saved",
        "step7_saved",
        "step8_saved",
        "step9_saved",
        "step10_saved",
        "step10b_saved",
        "step11_saved",
    ]

    # Clear all project-related keys
    for key in project_keys:
        if key in st.session_state:
            del st.session_state[key]

    # Reset to initial state
    st.session_state["current_step"] = 1
    st.session_state["last_step_completed"] = 0

# Check if we're creating a new project from dashboard
if st.session_state.get("creating_new_project", False):
    # Clear all project data when creating a new project
    if st.session_state.get("from_projects_dashboard", False):
        clear_all_project_data()
        # Clear the from_projects_dashboard flag after first use
        st.session_state.pop("from_projects_dashboard", None)

# Check if user has a current project
def get_current_project_info():
    """Get current project name and topic"""
    # If creating new project AND we don't have a project ID yet, return empty values
    if st.session_state.get("creating_new_project", False) and not st.session_state.get("current_project_id"):
        return {
            "project_name": "",
            "book_topic": ""
        }

    # Otherwise return actual project info (for navigation back to step1)
    project_name = st.session_state.get("current_project_name", "")
    book_topic = st.session_state.get("current_book_topic", "")

    # If we have argomento_keyword from step2, use that as book topic
    if not book_topic and st.session_state.get("argomento_keyword"):
        book_topic = st.session_state.get("argomento_keyword", "")
        st.session_state["current_book_topic"] = book_topic

    return {
        "project_name": project_name,
        "book_topic": book_topic
    }

# Progress bar
def show_progress():
    """Display progress bar"""
    st.progress(1/12, text=f"Step 1 of 12 - {int(min(1 / 11, 1.0) * 100)}% Complete")

show_progress()

# Test project creation form


# Titolo e sottotitolo

if get_current_project_info()["project_name"] and get_current_project_info()["book_topic"]:

    st.markdown(
        f"""
        <h1 style='text-align: center; color: #222; font-size: 2.5em;'>KDP GENIUS</h1>
        <h2 style='text-align: center; color: #222;'>Il tuo alleato intelligente per pubblicare bestseller su Amazon</h2>
        <div>&nbsp;</div>
        <p style='text-align: center; color: #444;'><b>Libro corrente: 📖 {get_current_project_info()["project_name"]}</b></p>

        <p style='text-align: center; color: #444;'>{get_current_project_info()["book_topic"]}</p>
        <div>&nbsp;</div>

        """,
        unsafe_allow_html=True,
    )
else:
    st.markdown(
        """
        <h1 style='text-align: center; color: #222; font-size: 2.5em;' >KDP GENIUS</h1>
        <h3 style='text-align: center; color: #444;' >Il tuo alleato intelligente per pubblicare bestseller su Amazon, passo dopo passo.</h3>
        <div>&nbsp;</div>
        """,
        unsafe_allow_html=True,
    )
# Marketplace and language definitions
marketplaces = {
    "IT": "Amazon.it",
    "US": "Amazon.com",
    "UK": "Amazon.co.uk",
    "DE": "Amazon.de",
    "FR": "Amazon.fr",
    "ES": "Amazon.es",
}

languages = {
    "it": "Italiano",
    "en": "English",
    "es": "Español",
    "fr": "Français",
    "de": "Deutsch",
}

# New project creation form
if not get_current_project_info()["project_name"]:
    st.markdown("### 📚 Crea Nuovo Progetto")
    st.info("💡 Inserisci i dettagli del tuo nuovo progetto editoriale")

    test_project_name = st.text_input(
        "Nome del Progetto",
        value="",
        key="test_project_name_input",
        placeholder="Es: Il Mio Primo Libro"
    )
    test_topic = st.text_input(
        "Argomento del Libro",
        value="",
        key="test_topic_input",
        placeholder="Es: Guida alla meditazione per principianti"
    )

    # Include marketplace and language in the project creation form
    col1, col2 = st.columns(2)
    with col1:
        # Get current marketplace, prioritizing session state
        current_marketplace = st.session_state.get("marketplace", "IT")

        try:
            marketplace_index = list(marketplaces.keys()).index(current_marketplace)
        except ValueError:
            marketplace_index = 0

        # Use selectbox with unique key to avoid conflicts
        marketplace_selection = st.selectbox(
            "Seleziona il marketplace di riferimento:",
            options=list(marketplaces.keys()),
            format_func=lambda x: f"{marketplaces[x]} ({x})",
            index=marketplace_index,
            key="step1_marketplace"
        )

        # Always update session state with current selection
        st.session_state["marketplace"] = marketplace_selection

    with col2:
        # Get current language, prioritizing session state
        current_language = st.session_state.get("lingua_target", "it")

        try:
            language_index = list(languages.keys()).index(current_language)
        except ValueError:
            language_index = 0

        # Use selectbox with unique key to avoid conflicts
        language_selection = st.selectbox(
            "Lingua del contenuto del libro:",
            options=list(languages.keys()),
            format_func=lambda x: languages[x],
            index=language_index,
            key="step1_language"
        )

        # Always update session state with current selection
        st.session_state["lingua_target"] = language_selection
else:
    # For existing projects, show current marketplace and language as info
    col1, col2 = st.columns(2)
    with col1:
        st.info(f"📍 Marketplace: {marketplaces.get(st.session_state.get('marketplace', 'IT'), 'Amazon.it')}")
    with col2:
        st.info(f"🌐 Lingua: {languages.get(st.session_state.get('lingua_target', 'it'), 'Italiano')}")
st.markdown("<div>&nbsp;</div>", unsafe_allow_html=True)
if st.button("🚀 INIZIA ORA", type="primary", use_container_width=True):
    # Check if we need to create a new project (no existing project)
    current_project = get_current_project_info()
    if not current_project["project_name"]:
        # Get values from the form inputs
        project_name = st.session_state.get("test_project_name_input", "").strip()
        book_topic = st.session_state.get("test_topic_input", "").strip()

        if not project_name or not book_topic:
            st.error("❌ Per favore inserisci sia il nome del progetto che l'argomento del libro prima di procedere!")
            st.stop()

        # Set the project info
        st.session_state["current_project_name"] = project_name
        st.session_state["current_book_topic"] = book_topic
        st.session_state["argomento_keyword"] = book_topic  # Also set argomento_keyword for step2
        # Clear new project flag if it exists
        st.session_state.pop("creating_new_project", None)
        # Clear form inputs after setting project info
        if "test_project_name_input" in st.session_state:
            del st.session_state["test_project_name_input"]
        if "test_topic_input" in st.session_state:
            del st.session_state["test_topic_input"]
        st.rerun()

    # Save step 1 data to current project or create new project
    try:
        marketplace = st.session_state.get("marketplace", "IT")
        lingua_target = st.session_state.get("lingua_target", "it")

        # Check if we have a current project loaded
        current_project_id = st.session_state.get("current_project_id")

        if current_project_id:
            # Update existing project using modular save method
            step_data = {
                "marketplace": marketplace,
                "lingua_target": lingua_target
            }

            user_id = get_user_id()
            result = get_auth_manager().save_step_data(user_id, current_project_id, 1, step_data)
            if result.get("success"):
                st.session_state["current_step"] = 2
                st.session_state["marketplace"] = marketplace
                st.session_state["lingua_target"] = lingua_target
            else:
                st.error(f"Error saving step data: {result.get('error', 'Unknown error')}")
                st.stop()

        elif st.session_state.get("current_project_name"):
            # Create new project if we have a name but no ID
            current_project_name = st.session_state.get("current_project_name")
            current_book_topic = st.session_state.get("current_book_topic", "")
            project_data = {
                "book_topic": current_book_topic,  # Save book topic in project data
                "step_data": {
                    "step1": {
                        "marketplace": marketplace,
                        "lingua_target": lingua_target,
                        "completed": True,
                        "timestamp": str(st.session_state.get("timestamp", ""))
                    },
                    "step2": {
                        "argomento_keyword": current_book_topic,  # Also save in step2 for consistency
                        "completed": False
                    }
                },
                "current_step": 2,
                "last_step_completed": 1
            }

            # Save new project
            user_id = get_user_id()
            result = get_auth_manager().save_project(user_id, st.session_state.get("current_project_name"), project_data) #type: ignore
            if result.get("success"):
                st.session_state["current_project_id"] = result.get("project_id")
                st.session_state["current_step"] = 2
                st.session_state["marketplace"] = marketplace
                st.session_state["lingua_target"] = lingua_target
                st.session_state["last_step_completed"] = 1
                # Clear new project flag after successful save
                st.session_state.pop("creating_new_project", None)
            else:
                st.error(f"Error creating project: {result.get('error', 'Unknown error')}")
                st.stop()

        else:
            # No project name, just continue with session state
            st.session_state["marketplace"] = marketplace
            st.session_state["lingua_target"] = lingua_target
            st.session_state["current_step"] = 2

    except Exception as e:
        st.error(f"Error saving step data: {str(e)}")
        st.stop()

    st.switch_page("pages/step2_argomento_keyword.py")

# Show help chat for step1
show_help_chat("step1_home")

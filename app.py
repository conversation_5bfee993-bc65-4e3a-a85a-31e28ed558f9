import streamlit as st

st.set_page_config(layout="wide")
from utils.config import config
import streamlit.components.v1 as components
from utils.auth import get_auth_manager
from utils.memory_optimization import periodic_cleanup, get_memory_optimizer
import gc
from components.help_chat import show_help_chat

st.markdown(
    """
<style>
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&family=Poppins:wght@400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap');

/* ═══ ROOT VARIABLES ═══ */
:root {
    --kdp-primary: #2F80ED;
    --kdp-primary-hover: #1E6FDB;
    --kdp-primary-light: #EBF4FF;
    --kdp-text-primary: #1E293B;
    --kdp-text-secondary: #64748B;
    --kdp-text-muted: #94A3B8;
    --kdp-bg-primary: #F8FAFC;
    --kdp-bg-secondary: #FFFFFF;
    --kdp-border: #E2E8F0;
    --kdp-border-light: #F1F5F9;
    --kdp-success: #10B981;
    --kdp-warning: #F59E0B;
    --kdp-error: #EF4444;
    --kdp-radius: 6px;
    --kdp-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --kdp-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --kdp-transition: 150ms ease-in-out;
}

/* ═══ GLOBAL TYPOGRAPHY ═══ */
html, body, [class*="css"] {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
    color: var(--kdp-text-primary) !important;
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Poppins', sans-serif !important;
    color: var(--kdp-text-primary) !important;
    font-weight: 600 !important;
    line-height: 1.3 !important;
    margin: 0.75rem 0 0.5rem 0 !important;
}

h1 { font-size: 1.75rem !important; }
h2 { font-size: 1.5rem !important; }
h3 { font-size: 1.25rem !important; }
h4 { font-size: 1.125rem !important; }
h5 { font-size: 1rem !important; }
h6 { font-size: 0.875rem !important; }

/* ═══ STREAMLIT COMPONENT IMPROVEMENTS ═══ */

/* Hide Streamlit branding */
.stAppToolbar, .stDecoration, .stStatusWidget { display: none !important; }

/* Metrics - Fix oversized appearance */
[data-testid="metric-container"] {
    background: var(--kdp-bg-secondary) !important;
    border: 1px solid var(--kdp-border) !important;
    border-radius: var(--kdp-radius) !important;
    padding: 0.75rem !important;
    box-shadow: var(--kdp-shadow-sm) !important;
    transition: var(--kdp-transition) !important;
}

[data-testid="metric-container"]:hover {
    border-color: var(--kdp-primary) !important;
    box-shadow: var(--kdp-shadow-md) !important;
}

[data-testid="metric-container"] > div {
    gap: 0.25rem !important;
}

[data-testid="metric-container"] [data-testid="metric-label"] {
    font-size: 0.75rem !important;
    font-weight: 500 !important;
    color: var(--kdp-text-secondary) !important;
    text-transform: uppercase !important;
    letter-spacing: 0.025em !important;
}

[data-testid="metric-container"] [data-testid="metric-value"] {
    font-size: 1.5rem !important;
    font-weight: 600 !important;
    color: var(--kdp-text-primary) !important;
    line-height: 1.2 !important;
}

[data-testid="metric-container"] [data-testid="metric-delta"] {
    font-size: 0.75rem !important;
    font-weight: 500 !important;
}

/* Buttons - Improved sizing and hierarchy */
.stButton > button {
    font-family: 'Inter', sans-serif !important;
    font-size: 0.875rem !important;
    font-weight: 500 !important;
    padding: 0.5rem 1rem !important;
    border-radius: var(--kdp-radius) !important;
    border: 1px solid var(--kdp-border) !important;
    background: var(--kdp-bg-secondary) !important;
    color: var(--kdp-text-primary) !important;
    transition: var(--kdp-transition) !important;
    box-shadow: var(--kdp-shadow-sm) !important;
    height: auto !important;
    min-height: 2.25rem !important;
}

.stButton > button:hover {
    border-color: var(--kdp-primary) !important;
    background: var(--kdp-primary-light) !important;
    box-shadow: var(--kdp-shadow-md) !important;
    transform: translateY(-1px) !important;
}

.stButton > button[kind="primary"] {
    background: var(--kdp-primary) !important;
    color: white !important;
    border-color: var(--kdp-primary) !important;
}

.stButton > button[kind="primary"]:hover {
    background: var(--kdp-primary-hover) !important;
    border-color: var(--kdp-primary-hover) !important;
}

.stButton > button[kind="secondary"] {
    background: transparent !important;
    color: var(--kdp-text-secondary) !important;
    border-color: var(--kdp-border) !important;
}

/* Form inputs - Better consistency */
.stTextInput > div > div > input,
.stTextArea > div > div > textarea,
.stSelectbox > div > div > div,
.stNumberInput > div > div > input {
    font-family: 'Inter', sans-serif !important;
    font-size: 0.875rem !important;
    padding: 0.5rem 0.75rem !important;
    border-radius: var(--kdp-radius) !important;
    border: 1px solid var(--kdp-border) !important;
    background: var(--kdp-bg-secondary) !important;
    transition: var(--kdp-transition) !important;
}

.stTextInput > div > div > input:focus,
.stTextArea > div > div > textarea:focus,
.stSelectbox > div > div > div:focus,
.stNumberInput > div > div > input:focus {
    border-color: var(--kdp-primary) !important;
    box-shadow: 0 0 0 3px rgba(47, 128, 237, 0.1) !important;
    outline: none !important;
}

/* Containers and spacing */
.stContainer > div {
    gap: 1rem !important;
}

.stColumns {
    gap: 1rem !important;
}

/* Sidebar improvements */
.stSidebar > div {
    padding-top: 1rem !important;
}

.stSidebar .stButton > button {
    width: 100% !important;
    margin-bottom: 0.5rem !important;
}

/* Enhanced background and layout */
.stApp {
    background: linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 100%) !important;
}

.stSidebar > div {
    background: linear-gradient(180deg, #FFFFFF 0%, #F8FAFC 100%) !important;
    border-right: 1px solid var(--kdp-border) !important;
}

/* Enhanced alerts and messages with better graphics */
.stAlert {
    border-radius: var(--kdp-radius) !important;
    border: none !important;
    font-size: 0.875rem !important;
    padding: 1rem !important;
    margin: 0.75rem 0 !important;
    box-shadow: var(--kdp-shadow-sm) !important;
    border-left: 4px solid !important;
}

.stSuccess {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.08) 0%, rgba(16, 185, 129, 0.12) 100%) !important;
    color: #065f46 !important;
    border-left-color: var(--kdp-success) !important;
}

.stWarning {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.08) 0%, rgba(245, 158, 11, 0.12) 100%) !important;
    color: #92400e !important;
    border-left-color: var(--kdp-warning) !important;
}

.stError {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.08) 0%, rgba(239, 68, 68, 0.12) 100%) !important;
    color: #991b1b !important;
    border-left-color: var(--kdp-error) !important;
}

.stInfo {
    background: linear-gradient(135deg, rgba(47, 128, 237, 0.08) 0%, rgba(47, 128, 237, 0.12) 100%) !important;
    color: #1e40af !important;
    border-left-color: var(--kdp-primary) !important;
}

/* Fixed height cards for projects */
[data-testid="stContainer"] > div > div > div[data-testid="stContainer"] {
    min-height: 400px !important;
    height: 400px !important;
    display: flex !important;
    flex-direction: column !important;
}

/* Ensure card content fills available space */
[data-testid="stContainer"] > div > div > div[data-testid="stContainer"] > div {
    flex: 1 !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: space-between !important;
}

/* Expanders */
.streamlit-expanderHeader {
    font-weight: 500 !important;
    font-size: 0.875rem !important;
}

/* Progress bars - Complete fix for appearance and labels */
.stProgress {
    height: 1.5rem !important;
    margin: 0.5rem 0 !important;
}

.stProgress > div {
    height: 1.5rem !important;
    background: var(--kdp-border-light) !important;
    border-radius: var(--kdp-radius) !important;
    overflow: visible !important;
    position: relative !important;
}

.stProgress > div > div {
    background: linear-gradient(90deg, var(--kdp-primary) 0%, var(--kdp-primary-hover) 100%) !important;
    border-radius: var(--kdp-radius) !important;
    height: 100% !important;
    min-height: 1.5rem !important;
    position: relative !important;
}

/* Progress bar text - Position outside the bar */
.stProgress > div > div > div,
.stProgress [data-testid="stMarkdownContainer"] {
    position: absolute !important;
    top: -1.75rem !important;
    left: 0 !important;
    right: 0 !important;
    background: transparent !important;
    color: var(--kdp-text-secondary) !important;
    font-size: 0.75rem !important;
    font-weight: 500 !important;
    text-align: left !important;
    padding: 0 !important;
    margin: 0 !important;
    line-height: 1 !important;
    z-index: 10 !important;
}

/* Alternative approach - hide the built-in text */
.stProgress .stMarkdown {
    display: none !important;
}

/* Tabs */
.stTabs [data-baseweb="tab-list"] {
    gap: 0.5rem !important;
}

.stTabs [data-baseweb="tab"] {
    font-size: 0.875rem !important;
    font-weight: 500 !important;
    padding: 0.5rem 1rem !important;
    border-radius: var(--kdp-radius) !important;
}

/* Enhanced containers and cards */
.stContainer {
    background: var(--kdp-bg-secondary) !important;
    border-radius: var(--kdp-radius) !important;
    box-shadow: var(--kdp-shadow-sm) !important;
}

/* Project cards - FIXED HEIGHT and MINIMAL spacing */
.project-card-wrapper {
    height: 420px !important;
    margin-bottom: 0.25rem !important;
    display: flex !important;
    flex-direction: column !important;
}

.project-card-wrapper [data-testid="stContainer"] {
    height: 420px !important;
    max-height: 420px !important;
    min-height: 420px !important;
    display: flex !important;
    flex-direction: column !important;
    background: var(--kdp-bg-secondary) !important;
    border: 1px solid var(--kdp-border) !important;
    border-radius: var(--kdp-radius) !important;
    box-shadow: var(--kdp-shadow-md) !important;
    transition: var(--kdp-transition) !important;
    overflow: hidden !important;
    padding: 0.75rem !important;
}

.project-card-wrapper [data-testid="stContainer"]:hover {
    box-shadow: var(--kdp-shadow-lg) !important;
    transform: translateY(-2px) !important;
    border-color: var(--kdp-primary) !important;
}

.project-card-wrapper [data-testid="stContainer"] > div {
    height: 100% !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: space-between !important;
}

/* DRASTICALLY reduce spacing between project rows - 95% reduction */
.project-row {
    margin-bottom: 0.1rem !important;
    padding: 0 !important;
}

.project-row .stColumns {
    gap: 0.25rem !important;
    margin-bottom: 0.1rem !important;
}

/* Remove ALL blue lines/dividers in project cards */
.project-card-wrapper hr,
.project-card-wrapper .stMarkdown hr,
.project-card-wrapper [data-testid="stHorizontalBlock"] hr,
.project-card-wrapper div[data-testid="stMarkdownContainer"] hr {
    display: none !important;
    visibility: hidden !important;
    height: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Text truncation for consistent heights */
.project-card-wrapper [data-testid="stMarkdownContainer"] p,
.project-card-wrapper .stMarkdown p {
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
    max-width: 100% !important;
    margin: 0.25rem 0 !important;
}

/* Force uniform card heights and minimal spacing */
.project-card-wrapper {
    box-sizing: border-box !important;
}

.project-card-wrapper [data-testid="stContainer"] {
    box-sizing: border-box !important;
}

/* Reduce ALL vertical spacing in project cards */
.project-card-wrapper .stMarkdown,
.project-card-wrapper [data-testid="stMarkdownContainer"],
.project-card-wrapper .stProgress,
.project-card-wrapper .stButton {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important;
}

/* Compact button layout */
.project-card-wrapper .stButton > button {
    padding: 0.25rem 0.5rem !important;
    font-size: 0.8rem !important;
    height: 2rem !important;
}

/* Enhanced sidebar design */
.stSidebar {
    background: linear-gradient(180deg, #FFFFFF 0%, #F8FAFC 100%) !important;
    border-right: 1px solid var(--kdp-border) !important;
}

.stSidebar > div {
    padding: 1rem 0.75rem !important;
}

/* Beautiful sidebar buttons */
.stSidebar .stButton > button {
    width: 100% !important;
    margin-bottom: 0.5rem !important;
    padding: 0.75rem 1rem !important;
    border-radius: 8px !important;
    border: 1px solid transparent !important;
    background: linear-gradient(135deg, var(--kdp-bg-secondary) 0%, #F8FAFC 100%) !important;
    color: var(--kdp-text-primary) !important;
    font-weight: 500 !important;
    font-size: 0.875rem !important;
    transition: all 200ms ease-in-out !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05) !important;
    text-align: left !important;
    justify-content: flex-start !important;
}

.stSidebar .stButton > button:hover {
    background: linear-gradient(135deg, var(--kdp-primary-light) 0%, #EBF4FF 100%) !important;
    border-color: var(--kdp-primary) !important;
    box-shadow: 0 4px 12px rgba(47, 128, 237, 0.15) !important;
    transform: translateY(-1px) !important;
    color: var(--kdp-primary) !important;
}

.stSidebar .stButton > button[kind="primary"] {
    background: linear-gradient(135deg, var(--kdp-primary) 0%, var(--kdp-primary-hover) 100%) !important;
    color: white !important;
    border-color: var(--kdp-primary) !important;
    box-shadow: 0 2px 8px rgba(47, 128, 237, 0.25) !important;
}

.stSidebar .stButton > button[kind="primary"]:hover {
    background: linear-gradient(135deg, var(--kdp-primary-hover) 0%, #1E6FDB 100%) !important;
    box-shadow: 0 6px 16px rgba(47, 128, 237, 0.35) !important;
    transform: translateY(-2px) !important;
}

.stSidebar .stButton > button[kind="secondary"] {
    background: transparent !important;
    border-color: var(--kdp-border) !important;
    color: var(--kdp-text-secondary) !important;
}

.stSidebar .stButton > button[kind="secondary"]:hover {
    background: var(--kdp-border-light) !important;
    border-color: var(--kdp-text-secondary) !important;
}

/* Enhanced visual hierarchy */
.main .block-container {
    padding-top: 2rem !important;
    padding-bottom: 2rem !important;
}

/* Better spacing for columns */
.stColumns > div {
    padding: 0 0.5rem !important;
}

/* Enhanced form styling */
.stForm {
    background: var(--kdp-bg-secondary) !important;
    border: 1px solid var(--kdp-border) !important;
    border-radius: var(--kdp-radius) !important;
    padding: 1.5rem !important;
    box-shadow: var(--kdp-shadow-sm) !important;
}
</style>
""",
    unsafe_allow_html=True,
)
# Define pages
auth_pages = [
    st.Page("pages/auth_login.py", title="Login", icon="🔐"),
    st.Page("pages/auth_signup.py", title="Sign Up", icon="📝"),
    st.Page("pages/auth_forgot_password.py", title="Forgot Password", icon="🔑"),
    st.Page("pages/dashboard/billing.py", title="Billing", icon="💰"),
    st.Page("pages/vision_builder.py", title="Vision Builder", icon="🎯"),
    st.Page("pages/step1_home.py", title="Home", icon="🏠"),
    st.Page("pages/auth_update_password.py", title="Update Password", icon="🔑"),
]

authenticated_pages = [
    st.Page("pages/dashboard/overview.py", title="Dashboard", icon="📊"),
    st.Page("pages/dashboard/profile.py", title="Profile", icon="👤"),
    st.Page("pages/dashboard/billing.py", title="Billing", icon="💰"),
    st.Page("pages/dashboard/projects.py", title="Projects", icon="📚"),
    st.Page("pages/vision_builder.py", title="Vision Builder", icon="🎯"),
    st.Page("pages/editorial_roadmap.py", title="Editorial Roadmap", icon="📚"),
    st.Page("pages/payment_success.py", title="Payment Success", icon="✅"),
    st.Page("pages/step1_home.py", title="Home", icon="🏠"),
    st.Page("pages/step2_argomento_keyword.py", title="Keywords", icon="🔑"),
    st.Page("pages/step3_recensioni.py", title="Reviews", icon="📉"),
    st.Page("pages/step4_buyer_persona.py", title="Buyer Persona", icon="👤"),
    st.Page("pages/step5_posizionamento.py", title="Positioning", icon="🎯"),
    st.Page("pages/step6_titolo_sottotitolo.py", title="Title", icon="📝"),
    st.Page("pages/step7_idea_analyzer.py", title="Idea Analyzer", icon="🔍"),
    st.Page("pages/step8_indice_perfetto.py", title="Index", icon="📘"),
    st.Page("pages/step9_descrizione_amazon.py", title="Description", icon="🛍️"),
    st.Page("pages/step10_cover_perfetta.py", title="Cover", icon="🎨"),
    st.Page("pages/step10b_design_cover.py", title="Cover Design", icon="🖼️"),
    st.Page("pages/step11_riepilogo.py", title="Summary", icon="📋"),
    st.Page("pages/step12_scarica_word.py", title="Export", icon="📥"),
    st.Page("pages/auth_update_password.py", title="Update Password", icon="🔑"),
]


# Auto-refreshing memory cleanup
@st.fragment(run_every="5m")
def auto_memory_cleanup():
    """Run periodic memory cleanup every 5 minutes"""
    periodic_cleanup()


# Auto-refreshing credits display
@st.fragment(run_every="10s")
def credits_display(email: str, name: str):
    """Auto-refreshing credits display"""
    if st.session_state.get("authenticated", False):
        user_data = st.session_state.get("user_data", {})
        user_id = user_data.get("id")

        if user_id:
            try:
                user_profile = get_auth_manager().get_user_profile(user_id)
                if user_profile:
                    credits = user_profile.get("credits", 0)

                    # Color coding for credits
                    if credits >= 50:
                        color = "#2ca02c"  # Green
                        icon = "💳"
                    elif credits >= 20:
                        color = "#ff7f0e"  # Orange
                        icon = "⚠️"
                    else:
                        color = "#d62728"  # Red
                        icon = "🚨"

                    st.markdown(
                        f"""
                        <div style='border: 1px solid {color}44; border-radius: 8px; padding: 12px; margin: 8px 0; text-align: center;'>
                            <div>{str(name)}</div>
                            <div style="font-size: 13px;">{str(email)}</div>
                            <div style="color: {color}; font-weight: bold; font-size: 18px;">{icon} {credits} Crediti</div>
                        </div>
                        """,
                        unsafe_allow_html=True,
                    )

                    # Display memory usage in sidebar
                    optimizer = get_memory_optimizer()
                    memory_stats = optimizer.get_memory_usage()
                    if memory_stats["percent"] > 70:
                        st.markdown(
                            f"""
                            <div style='border: 1px solid #ff7f0e44; border-radius: 8px; padding: 8px; margin: 8px 0; text-align: center;'>
                                <div style="color: #ff7f0e; font-size: 14px;">💾 Memory: {memory_stats['rss_mb']:.0f}MB ({memory_stats['percent']:.0f}%)</div>
                            </div>
                            """,
                            unsafe_allow_html=True,
                        )

                    if credits < 20:
                        st.warning("⚠️ I tuoi crediti sono pochi!")
                        if st.button("💰 Acquistaa Crediti", use_container_width=True):
                            st.switch_page("pages/dashboard/billing.py")

            except Exception as e:
                st.error(f"Error loading credits: {str(e)}")


# Initialize session state with defaults (will be overridden by project data when loaded)
def initialize_session_defaults():
    """Initialize default session state values"""
    if "marketplace" not in st.session_state:
        st.session_state["marketplace"] = "IT"

    if "lingua_target" not in st.session_state:
        st.session_state["lingua_target"] = "it"

    if "authenticated" not in st.session_state:
        st.session_state["authenticated"] = False


# Check authentication status with Supabase Auth persistence
def get_authentication_status():
    """Get current authentication status"""
    return st.session_state.get("authenticated", False)


def handle_authentication():
    """Handle authentication and session restoration"""
    is_authenticated = get_authentication_status()
    query_params = st.query_params

    # Try to restore session from Supabase Auth if not authenticated
    if not is_authenticated:
        # First check if there's a token in URL params (from Stripe redirect)
        refresh_token = query_params.get("token")

        if refresh_token:
            # Get fresh access token from refresh token
            auth_mgr = get_auth_manager()
            fresh_access_token = auth_mgr.get_fresh_access_token_from_refresh(
                refresh_token
            )

            if fresh_access_token:
                # Try to restore session using fresh access token
                is_authenticated = auth_mgr.restore_session_from_token(
                    fresh_access_token
                )
                if is_authenticated:
                    # Force refresh user data from database after token restoration
                    user_data = st.session_state.get("user_data", {})
                    user_id = user_data.get("id")
                    if user_id:
                        get_auth_manager().refresh_user_data(user_id)

                    # Clear token from URL for security
                    new_params = {k: v for k, v in query_params.items() if k != "token"}
                    st.query_params.clear()
                    for k, v in new_params.items():
                        st.query_params[k] = v
                    st.rerun()
                else:
                    st.error(
                        "❌ Failed to restore authentication from fresh access token"
                    )
            else:
                st.error("❌ Failed to get fresh access token from refresh token")
        else:
            # Try normal Supabase session restoration
            is_authenticated = get_auth_manager().restore_session_from_supabase()

    return is_authenticated


def handle_stripe_redirects(is_authenticated):
    """Handle Stripe success/cancel redirects"""
    query_params = st.query_params

    if is_authenticated and query_params:
        if "success" in query_params:
            session_id = query_params.get("session_id")

            # Show success message at the top
            st.success(
                "🎉 Payment successful! Your credits have been added to your account."
            )
            st.balloons()

            if session_id:

                from utils.stripe_utils import stripe_manager

                success = stripe_manager.handle_successful_payment(session_id)

                if not success:
                    st.error("❌ There was an issue processing your payment.")
            else:
                st.error("🔍 DEBUG - No session_id found in query params!")

        elif "canceled" in query_params:
            st.warning("❌ Payment was canceled. You can try again anytime.")
            st.query_params.clear()


def handle_recovery_redirect():
    """Handle password recovery redirects"""
    query_params = st.query_params
    access_token = query_params.get("access_token")
    token_type = query_params.get("type")
    refresh_token = query_params.get("refresh_token")

    if token_type == "recovery" and access_token:
        # Show password update form directly
        st.markdown(
            """
            <h1 style='text-align: center; color: #222; font-size: 3em;'>🔐 Set New Password</h1>
            <h3 style='text-align: center; color: #444;'>Choose a strong password for your account</h3>
            """,
            unsafe_allow_html=True,
        )

        with st.container():
            with st.form("update_password_form"):
                new_password = st.text_input(
                    "New Password", type="password", placeholder="Enter new password"
                )
                confirm_password = st.text_input(
                    "Confirm Password",
                    type="password",
                    placeholder="Confirm new password",
                )

                st.markdown(
                    """
                **Password must:**
                - Be at least 8 characters long
                - Include uppercase and lowercase letters
                - Include at least one number
                """
                )

                update_button = st.form_submit_button(
                    "🔐 Update Password", type="primary", use_container_width=True
                )

            if update_button:
                if not new_password or not confirm_password:
                    st.error("Please fill in both password fields.")
                elif new_password != confirm_password:
                    st.error("Passwords do not match. Please try again.")
                elif len(new_password) < 8:
                    st.error("Password must be at least 8 characters long.")
                else:
                    try:
                        from supabase import Client

                        auth_mgr = get_auth_manager()
                        supabase_client: Client = auth_mgr.conn

                        # Set the session with the recovery token
                        supabase_client.auth.set_session(
                            access_token, refresh_token if refresh_token else ""
                        )

                        # Update the password
                        response = supabase_client.auth.update_user(
                            attributes={"password": new_password}
                        )

                        if response:
                            st.success("✅ Password aggiornata con successo!")
                            st.info("Ti reindirizziamo alla pagina di accesso...")

                            # Clear URL parameters
                            st.query_params.clear()

                            # Redirect using HTML meta refresh
                            st.markdown(
                                '<meta http-equiv="refresh" content="2;url=/">',
                                unsafe_allow_html=True,
                            )
                        else:
                            st.error(
                                "C'è stato un errore durante l'aggiornamento della password. Si prega di riprovare."
                            )

                    except Exception as e:
                        st.error(f"Errore aggiornando la password: {str(e)}")
                        st.info(
                            "Il link è scaduto. Prova ancora a richiederne uno nuovo."
                        )

        # Stop execution to prevent navigation
        st.stop()


# Handle post-authentication redirects
def handle_post_auth_redirects():
    """Handle redirects after authentication"""
    user_data = st.session_state.get("user_data", {})
    user_id = user_data.get("id")

    # Check if we need to redirect after successful auth
    if user_id:
        # Vision builder is now optional - no automatic redirect
        pass


def get_user_info():
    """Get current user information"""
    user_data = st.session_state.get("user_data", {})
    return {
        "user_data": user_data,
        "user_id": user_data.get("id"),
        "credits": user_data.get("credits", 0),
    }


def setup_authenticated_user():
    """Setup user data for authenticated users"""
    if get_authentication_status():
        handle_post_auth_redirects()
        user_info = get_user_info()
        return user_info
    return None


def render_sidebar():
    """Render sidebar navigation for authenticated users"""
    if not get_authentication_status():
        return

    user_info = get_user_info()
    user_data = user_info["user_data"]

    # Sidebar navigation
    with st.sidebar:
        # Logo PNG display
        try:
            st.markdown(
                """
                <div style="text-align: center; padding: 0.75rem 0;">
                    <img src="./app/static/kdp-o.png" style="max-width: 120px; height: auto; display: block; margin: 0 auto;">
                </div>
                """,
                unsafe_allow_html=True,
            )
        except:
            # Fallback to styled text logo if image not found
            st.markdown(
                """
                <div style="text-align: center; padding: 0.75rem 0;">
                    <h1 style="color: var(--kdp-primary); font-weight: 600; margin: 0; font-size: 1.25rem;">KDP GENIUS</h1>
                    <p style="color: var(--kdp-text-secondary); font-size: 0.75rem; margin: 0.25rem 0 0 0;">Your Publishing Ally</p>
                </div>
                """,
                unsafe_allow_html=True,
            )
        name = user_data.get("name", "User")
        email = user_data.get("email", "N/A")
        # Auto-refreshing credits display
        credits_display(email, name)

        st.markdown("---")

        # Dashboard navigation buttons
        if st.button(
            "📊 Dashboard",
            use_container_width=True,
        ):
            st.switch_page("pages/dashboard/overview.py")

        if st.button("👤 Profilo", use_container_width=True, key="nav_profile"):
            st.switch_page("pages/dashboard/profile.py")

        if st.button(
            "💰 Crediti & Pagamenti", use_container_width=True, key="nav_billing"
        ):
            st.switch_page("pages/dashboard/billing.py")

        if st.button(
            "📚 Progetti di Libri", use_container_width=True, key="nav_projects"
        ):
            st.switch_page("pages/dashboard/projects.py")

        if st.button("🎯 Crea Autore", use_container_width=True, key="nav_vision"):
            st.switch_page("pages/vision_builder.py")

        if st.button(
            "📚 Crea Una Collana", use_container_width=True, key="nav_roadmap"
        ):
            st.switch_page("pages/editorial_roadmap.py")

        # Logout button
        st.markdown("---")
        if st.button(
            "🚪 Esci", use_container_width=True, type="secondary", key="nav_logout"
        ):
            get_auth_manager().logout_user()
            st.rerun()


# Determine which pages to show
def get_pages():
    """Get appropriate pages based on authentication status"""
    if get_authentication_status():
        return authenticated_pages
    else:
        return auth_pages


def main():
    """Main application entry point"""
    # Initialize defaults
    initialize_session_defaults()

    # Handle password recovery redirects BEFORE authentication
    handle_recovery_redirect()

    # Handle authentication
    is_authenticated = handle_authentication()

    # Handle Stripe redirects
    handle_stripe_redirects(is_authenticated)

    # Setup authenticated user
    user_info = setup_authenticated_user()

    # Render sidebar
    render_sidebar()

    # Get pages and create navigation
    pages = get_pages()
    pg = st.navigation(pages)

    pg.run()

    # Help chat is now handled by individual pages that need it
    # This prevents wrong help popups from appearing on unrelated pages


# Run the app
main()

import gc
import psutil
import streamlit as st
import functools
import time
from typing import Any, Callable, Optional
import sys
import os


class MemoryOptimizer:
    """Memory optimization utilities for Streamlit applications"""

    def __init__(self):
        self.process = psutil.Process()
        self.initial_memory = self.get_memory_usage()

    def get_memory_usage(self) -> dict:
        """Get current memory usage statistics"""
        memory_info = self.process.memory_info()
        return {
            "rss_mb": memory_info.rss / 1024 / 1024,  # Resident Set Size in MB
            "vms_mb": memory_info.vms / 1024 / 1024,  # Virtual Memory Size in MB
            "percent": self.process.memory_percent()
        }

    def get_memory_stats(self) -> str:
        """Get formatted memory statistics string"""
        stats = self.get_memory_usage()
        return f"Memory: {stats['rss_mb']:.1f}MB ({stats['percent']:.1f}%)"

    def collect_garbage(self, generation: Optional[int] = None) -> int:
        """
        Run garbage collection and return number of objects collected

        Args:
            generation: Specific generation to collect (0, 1, or 2). None collects all.
        """
        if generation is not None:
            return gc.collect(generation)
        return gc.collect()

    def clear_streamlit_cache(self):
        """Clear Streamlit's cache"""
        try:
            st.cache_data.clear()
            st.cache_resource.clear()
        except Exception:
            # Fallback for older Streamlit versions
            st.error("Failed to clear Streamlit cache")

    def optimize_session_state(self, exclude_keys: Optional[list] = None):
        """
        Clean up large objects in session state

        Args:
            exclude_keys: List of keys to exclude from cleanup
        """
        if exclude_keys is None:
            exclude_keys = ["authenticated", "user_data", "current_project_id", "memory_optimizer"]

        for key in list(st.session_state.keys()):
            if key not in exclude_keys:
                value = st.session_state.get(key)
                # Check if it's a large object (images, dataframes, etc.)
                try:
                    if value is not None and sys.getsizeof(value) > 5 * 1024 * 1024:  # 5MB
                        del st.session_state[key]
                except:
                    # Skip if getsizeof fails
                    pass

    def clear_temp_files(self, temp_dir: str = "temp"):
        """Clear temporary files created during processing"""
        if os.path.exists(temp_dir):
            for file in os.listdir(temp_dir):
                file_path = os.path.join(temp_dir, file)
                try:
                    if os.path.isfile(file_path):
                        os.unlink(file_path)
                except Exception:
                    pass


def memory_cleanup(func: Callable) -> Callable:
    """
    Decorator to automatically clean up memory after function execution

    Usage:
        @memory_cleanup
        def generate_large_content():
            # Your function code
            pass
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            result = func(*args, **kwargs)
            return result
        finally:
            # Force garbage collection after function execution
            gc.collect()

    return wrapper


def memory_tracked(func: Callable) -> Callable:
    """
    Decorator to track memory usage of a function

    Usage:
        @memory_tracked
        def memory_intensive_function():
            # Your function code
            pass
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        optimizer = MemoryOptimizer()
        before = optimizer.get_memory_usage()

        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()

        after = optimizer.get_memory_usage()

        # Log memory usage
        memory_increase = after['rss_mb'] - before['rss_mb']
        if memory_increase > 50:  # Log if increase is more than 50MB
            st.warning(f"⚠️ Function '{func.__name__}' increased memory by {memory_increase:.1f}MB")

        return result

    return wrapper


def cleanup_large_objects(*objects: Any):
    """
    Explicitly delete large objects and run garbage collection

    Usage:
        cleanup_large_objects(large_dataframe, big_image, ai_response)
    """
    for obj in objects:
        del obj
    gc.collect()


def optimize_image_memory(image_data: bytes, max_size_mb: float = 5.0) -> Optional[bytes]:
    """
    Optimize image memory by compressing if too large

    Args:
        image_data: Image data in bytes
        max_size_mb: Maximum size in MB

    Returns:
        Optimized image data or None if optimization fails
    """
    from PIL import Image
    from io import BytesIO

    size_mb = len(image_data) / 1024 / 1024
    if size_mb <= max_size_mb:
        return image_data

    try:
        # Open and compress image
        img = Image.open(BytesIO(image_data))

        # Convert RGBA to RGB if necessary
        if img.mode == 'RGBA':
            background = Image.new('RGB', img.size, (255, 255, 255))
            background.paste(img, mask=img.split()[3])
            img = background

        # Compress with quality adjustment
        output = BytesIO()
        quality = int(95 * (max_size_mb / size_mb))
        img.save(output, format='JPEG', quality=max(quality, 60), optimize=True)

        return output.getvalue()
    except Exception:
        return None


# Streamlit-specific memory management functions
def periodic_cleanup():
    """
    Run periodic cleanup tasks
    Best used with st.fragment run_every parameter

    Usage:
        @st.fragment(run_every="5m")
        def cleanup_task():
            periodic_cleanup()
    """
    optimizer = MemoryOptimizer()

    # Check memory usage
    memory_stats = optimizer.get_memory_usage()

    # If memory usage is high, perform cleanup
    if memory_stats['percent'] > 80:
        # Clear old session state items
        optimizer.optimize_session_state()

        # Run garbage collection
        optimizer.collect_garbage()

        # Clear Streamlit cache if needed
        if memory_stats['percent'] > 90:
            optimizer.clear_streamlit_cache()


def cleanup_after_step(step_number: int):
    """
    Clean up memory after completing a step in the workflow

    Args:
        step_number: The step number that was just completed
    """
    # Step-specific cleanup
    cleanup_keys = {
        3: ["recensioni_raw", "amazon_response"],  # After reviews step
        6: ["titoli_sottotitoli_generati"],  # After title generation
        8: ["indice_configurazione"],  # After index generation
        10: ["cover_ideas_raw"],  # After cover suggestions
        11: ["riepilogo_temp"],  # After summary
    }

    keys_to_remove = cleanup_keys.get(step_number, [])

    for key in keys_to_remove:
        if key in st.session_state:
            del st.session_state[key]

    # Always run garbage collection after step completion
    gc.collect()


# Create a global optimizer instance (safe for Streamlit)
def get_memory_optimizer() -> MemoryOptimizer:
    """Get or create memory optimizer instance"""
    if "memory_optimizer" not in st.session_state:
        st.session_state["memory_optimizer"] = MemoryOptimizer()
    return st.session_state["memory_optimizer"]

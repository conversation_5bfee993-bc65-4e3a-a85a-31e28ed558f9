import streamlit as st

from utils.auth import get_auth_manager
import os
st.markdown(
    """
    <style>
    /* Professional auth form styling */
    .stMainBlockContainer  {
        height: 100vh;
        margin: auto;
        vertical-align: middle;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        max-width: 480px !important;
        padding-top: 2rem !important;
        padding-left: 1rem !important;
        padding-right: 1rem !important;
    }


    /* Mobile styling */
    @media (max-width: 480px) {
      .stMainBlockContainer  {
        padding-top: 200px;
        }

        h1 {
            font-size: 2.2em !important;
        }

        h3 {
            font-size: 1.1em !important;
            line-height: 1.5em !important;
        }
    }

    /* Form elements styling */
    .stTextInput > div > div > input {
        font-size: 16px !important;
    }

    .stButton > button {
        width: 100% !important;
        font-weight: 500 !important;
    }
    </style>
    """,
    unsafe_allow_html=True,
)
# ──────
# Title
st.markdown(
    """
    <h1 style='text-align: center; color: #222; font-size: 3em;'>🔐 Login</h1>
    <h3 style='text-align: center; color: #444;'>Accedi al tuo account KDP GENIUS</h3>
    """,
    unsafe_allow_html=True,
)

# Check if user is already authenticated (try Supabase Auth first)
if not st.session_state.get("authenticated", False):
    get_auth_manager().restore_session_from_supabase()

if st.session_state.get("authenticated", False):
    st.success("Sei già loggato")
    user_data = st.session_state.get("user_data", {})
    st.info(f"Welcome back, {user_data.get('name', 'User')}!")

    col1, col2 = st.columns(2)
    with col1:
        if st.button("📊 Vai alla Dashboard", type="primary"):
            st.switch_page("pages/dashboard/overview.py")
            st.rerun()
    with col2:
        if st.button("📚 Inizia un nuovo libro"):
            st.switch_page("pages/dashboard/projects.py")
            st.rerun()
    st.stop()

# Login form
with st.container():

    with st.form("login_form"):
        email = st.text_input("Email", placeholder="<EMAIL>")
        password = st.text_input("Password", type="password", placeholder="Inserisci la tua password")

        col1, col2 = st.columns([1, 1])
        with col1:
            login_button = st.form_submit_button("🚀 Accedi", type="primary", use_container_width=True)
        with col2:
            forgot_password = st.form_submit_button("🔑 Hai dimenticato la password?", use_container_width=True)

    # Handle login
    if login_button:
        if not email or not password:
            st.error("Perfavore inserisci email e password")
        else:
            with st.spinner("Logging in.."):
                result = get_auth_manager().login_user(email, password)

                if result["success"]:
                    # Session state is already set by login_user method
                    st.success("Login effetuato con successo!")


                    # Vision builder is now optional - proceed to dashboard
                    st.rerun()
                else:
                    st.error(result["message"])

    # Handle forgot password
    if forgot_password:
           st.switch_page("pages/auth_forgot_password.py")
# Navigation links
st.markdown("---")
col1, col2 = st.columns(2)

with col1:
    st.markdown("**Non hai un account?**")
    if st.button("📝 Sign Up", use_container_width=True):
        st.switch_page("pages/auth_signup.py")


with col2:
    st.markdown("**Bisogno di aiuto?**")
    if st.button("❓ Supporto", use_container_width=True):
        st.switch_page("pages/auth_forgot_password")

# Footer
st.markdown("---")
st.markdown(
    """
    <div style="text-align: center; color: #666; font-size: 0.8em;">
        <p>© 2024 KDP GENIUS - Your intelligent publishing ally</p>
    </div>
    """,
    unsafe_allow_html=True,
)

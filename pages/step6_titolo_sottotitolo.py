from utils.auth import get_auth_manager
import streamlit as st
from utils.credits import get_user_id, get_user_credits
from components.help_chat import show_help_chat

# Require authentication
get_auth_manager().require_auth()


import os
import streamlit as st
from openai import OpenAI
from utils.config import config



def genera_titoli_sottotitoli(
    argomento, buyer_persona, problemi_recensioni, posizionamento, lingua, openai_key
):
    prompt = f"""
    Sei il miglior copywriter professionista multilingue al mondo, specializzato in titoli editoriali per Amazon KDP. Il tuo compito è creare titoli e sottotitoli in lingua {lingua}.

    📌 IMPORTANTE: TUTTI i titoli e sottotitoli devono essere ASSOLUTAMENTE in {lingua}

    Crea 5 combinazioni di Titolo + Sottotitolo per un libro basato su:
    - Argomento: {argomento}
    - Buyer Persona: {buyer_persona}...
    - Recensioni negative: {problemi_recensioni}...
    - Posizionamento: {posizionamento}...
    - 🌐 Lingua richiesta: {lingua}

    Ogni combinazione deve seguire questo formato:
    Titolo: [titolo accattivante max 12 parole IN {lingua.upper()}]
    Sottotitolo: [spiega beneficio o contenuto in max 18 parole IN {lingua.upper()}]

    📌 Requisiti:
    - I titoli e sottotitoli devono essere ESCLUSIVAMENTE in {lingua}
    - Linguaggio persuasivo e parole chiave appropriate per il mercato {lingua}
    - Tono coerente col pubblico target
    - Nessun preambolo o spiegazione aggiuntiva
    """
    client = OpenAI(api_key=openai_key)
    response = client.chat.completions.create(
        model="gpt-4o",
        messages=[
            {
                "role": "system",
                "content": f"Sei un copywriter esperto multilingue in titoli editoriali per Amazon KDP. Rispondi SEMPRE nella lingua richiesta.",
            },
            {"role": "user", "content": prompt},
        ],
        temperature=0.85,
        max_tokens=1200,
    )
    return (
        response.choices[0].message.content.strip()
        if response.choices[0].message.content
        else ""
    )


LINGUE = {
    "it": "italiano",
    "en": "inglese",
    "es": "spagnolo",
    "fr": "francese",
    "de": "tedesco",
}

# Language will be accessed inside button handler


# Progress bar function
def show_progress():
    """Display progress bar"""
    current = st.session_state.get("current_step", 6)
    st.progress(min(current / 12, 1.0), text=f"{int(current / 12 * 100)}% completato")

show_progress()

st.markdown(
    """

    <h1 style="text-align:center;">Step 6: Titolo & Sottotitolo</h1>
    <h2 style="text-align:center; ">
        Genera idee con l'AI, seleziona una proposta o inserisci la tua combinazione personalizzata.
    </h2>

""",
    unsafe_allow_html=True,
)

# Display language info
st.info(f"🌐 Lingua del titolo e sottotitolo: {LINGUE.get(st.session_state.get('lingua_target', 'it'), 'italiano').capitalize()}")

# Get language from global setting


# Check prerequisites but don't block navigation
required_keys = [
    "argomento_keyword",
    "buyer_persona_generata",
    "recensioni_analizzate",
    "posizionamento_editoriale",
]
missing_prerequisites = []
for key in required_keys:
    if key not in st.session_state:
        step_names = {
            "argomento_keyword": "Step 2: Argomento & Keyword",
            "buyer_persona_generata": "Step 4: Buyer Persona",
            "recensioni_analizzate": "Step 3: Analisi Recensioni",
            "posizionamento_editoriale": "Step 5: Posizionamento"
        }
        missing_prerequisites.append(step_names.get(key, key))

if missing_prerequisites:
    st.warning(f"⚠️ Completa prima: {', '.join(missing_prerequisites)}")
    prerequisites_met = False
else:
    prerequisites_met = True


if prerequisites_met and st.button("✨ Genera Titoli & Sottotitoli (5 crediti)", type="primary"):
    # Check credits before generation
    if get_user_credits() < 5:
        st.error("❌ Crediti insufficienti! La generazione di titoli e sottotitoli richiede 5 crediti.")
        st.info("💳 Puoi acquistare più crediti dalla dashboard.")
    else:
        with st.spinner("Generazione in corso..."):
            try:
                titoli = genera_titoli_sottotitoli(
                    argomento=st.session_state["argomento_keyword"],
                    buyer_persona=st.session_state["buyer_persona_generata"],
                    problemi_recensioni=st.session_state.get("recensioni_analizzate"),
                    posizionamento=st.session_state.get("posizionamento_editoriale"),
                    lingua=LINGUE.get(st.session_state.get("lingua_target", "it"), "italiano"),

                    openai_key=config.OPENAI_API_KEY,
                )
                st.session_state["titoli_sottotitoli_generati"] = titoli

                # Deduct credits for successful generation
                user_id = get_user_id()
                get_auth_manager().deduct_credits(user_id, "content_generation", 5, description="Title and subtitle generation")

                st.success("✅ Titoli e sottotitoli generati!")
                st.rerun()
            except Exception as e:
                st.error(f"❌ Errore durante la generazione AI: {e}")

proposte_ai = []
if "titoli_sottotitoli_generati" in st.session_state:
    # First try to parse structured format
    blocchi = [
        b.strip()
        for b in st.session_state["titoli_sottotitoli_generati"].split("\n\n")
        if b.strip()
    ]
    for blocco in blocchi:
        # More flexible parsing - case insensitive and handles variations
        blocco_lower = blocco.lower()
        if ("titolo:" in blocco_lower or "title:" in blocco_lower) and ("sottotitolo:" in blocco_lower or "subtitle:" in blocco_lower):
            proposte_ai.append(blocco)
        elif blocco.count("\n") >= 1 and len(blocco.split("\n")) >= 2:
            # If it has at least 2 lines, treat as title/subtitle pair
            proposte_ai.append(blocco)

    # If no structured format found, show raw output
    if not proposte_ai and st.session_state["titoli_sottotitoli_generati"].strip():
        st.warning("⚠️ Formato AI non riconosciuto. Ecco l'output grezzo:")
        st.text_area("Output AI:", st.session_state["titoli_sottotitoli_generati"], height=300, disabled=True)

if proposte_ai:
    st.markdown("### 🎯 Seleziona una delle combinazioni proposte dall'AI:")

    # Add regenerate option
    if prerequisites_met and st.button("🔄 Rigenera Titoli (5 crediti)", use_container_width=True):
        st.session_state.pop("titoli_sottotitoli_generati", None)
        st.rerun()

    scelta = st.radio("Scegli una combinazione AI:", proposte_ai, key="scelta_ai")
    if st.button("💾 Salva questa combinazione AI"):
        try:
            lines = scelta.split("\n")

            titolo = lines[0].split(":", 1)[1].strip() if len(lines) > 0 and ":" in lines[0] else ""
            sottotitolo = (
                lines[1].split(":", 1)[1].strip() if len(lines) > 1 and ":" in lines[1] else ""
            )

            # Remove extra quotes if present
            titolo = titolo.strip('"').strip("'").strip()
            sottotitolo = sottotitolo.strip('"').strip("'").strip()

            st.session_state["titolo_scelto"] = titolo
            st.session_state["sottotitolo_scelto"] = sottotitolo

            # Save step 6 data
            current_project_id = st.session_state.get("current_project_id")
            if current_project_id:
                step_data = {
                    "titolo_scelto": titolo,
                    "sottotitolo_scelto": sottotitolo,
                    "titoli_sottotitoli_generati": st.session_state.get("titoli_sottotitoli_generati", "")
                }
                user_id = get_user_id()
                result = get_auth_manager().save_step_data(user_id, current_project_id, 6, step_data)

                if result.get("success"):
                    st.success("✅ Titolo e sottotitolo salvati dalla selezione AI!")
                else:
                    st.error(f"❌ Errore nel salvataggio: {result.get('error', 'Unknown error')}")
        except Exception:
            st.error(
                "Errore nel salvataggio. Controlla il formato delle proposte AI."
            )

st.markdown("---")
st.markdown("#### ✍️ Oppure inserisci la tua combinazione personalizzata:")
titolo_custom = st.text_input(
    "Titolo scelto",
    value=st.session_state.get("titolo_scelto", "")
)
sottotitolo_custom = st.text_area(
    "Sottotitolo scelto",
    value=st.session_state.get("sottotitolo_scelto", "")
)

if st.button("💾 Salva scelta manuale"):
    # titolo_custom and sottotitolo_custom are already defined above
    if not titolo_custom.strip() or not sottotitolo_custom.strip():
        st.error("Inserisci sia il titolo che il sottotitolo.")
    else:
        # Clean quotes from manual input too
        titolo_clean = titolo_custom.strip().strip('"').strip("'").strip()
        sottotitolo_clean = sottotitolo_custom.strip().strip('"').strip("'").strip()

        st.session_state["titolo_scelto"] = titolo_clean
        st.session_state["sottotitolo_scelto"] = sottotitolo_clean

        # Save step 6 data
        current_project_id = st.session_state.get("current_project_id")
        if current_project_id:
            step_data = {
                "titolo_scelto": titolo_clean,
                "sottotitolo_scelto": sottotitolo_clean,
                "titoli_sottotitoli_generati": st.session_state.get("titoli_sottotitoli_generati", "")
            }
            user_id = get_user_id()
            result = get_auth_manager().save_step_data(user_id, current_project_id, 6, step_data)
            st.write(f"🔍 DEBUG - Save result: {result}")

            if result.get("success"):
                st.success("✅ Titolo e sottotitolo salvati!")
            else:
                st.error(f"❌ Errore nel salvataggio: {result.get('error', 'Unknown error')}")

if st.session_state.get("titolo_scelto") and st.session_state.get(
    "sottotitolo_scelto"
):
    st.markdown("### ✅ Titolo e Sottotitolo scelti:")
    st.markdown(f"**Titolo:** {st.session_state['titolo_scelto']}")
    st.markdown(f"**Sottotitolo:** {st.session_state['sottotitolo_scelto']}")

# Always show navigation
st.markdown("---")
col1, col2 = st.columns(2)

with col1:
    if st.button("⬅️ Indietro", use_container_width=True):
        st.session_state["current_step"] = 5
        st.switch_page("pages/step5_posizionamento.py")

with col2:
    if st.session_state.get("titolo_scelto") and st.session_state.get("sottotitolo_scelto"):
        if st.button("✅ Conferma e continua", type="primary", use_container_width=True):
            # Ensure data is saved before proceeding
            current_project_id = st.session_state.get("current_project_id")
            st.write(f"🔍 DEBUG - Continue - current_project_id: {current_project_id}, step6_saved: {st.session_state.get('step6_saved')}")
            if current_project_id and not st.session_state.get("step6_saved"):
                step_data = {
                    "titolo_scelto": st.session_state.get("titolo_scelto", ""),
                    "sottotitolo_scelto": st.session_state.get("sottotitolo_scelto", ""),
                    "titoli_sottotitoli_generati": st.session_state.get("titoli_sottotitoli_generati", "")
                }
                user_id = get_user_id()
                result = get_auth_manager().save_step_data(user_id, current_project_id, 6, step_data)
                if result.get("success"):
                    st.session_state["step6_saved"] = True
                    st.session_state["current_step"] = 7
                    st.switch_page("pages/step7_idea_analyzer.py")
                else:
                    st.error(f"❌ Errore nel salvataggio prima di procedere: {result.get('error', 'Unknown error')}")
    else:
        st.info("📝 Seleziona o inserisci titolo e sottotitolo per continuare")

# Show help chat for step6
show_help_chat("step6_titolo_sottotitolo")

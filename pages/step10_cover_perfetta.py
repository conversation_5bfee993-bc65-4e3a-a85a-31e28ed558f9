from utils.auth import get_auth_manager
import streamlit as st
from utils.credits import get_user_id, get_user_credits
from components.help_chat import show_help_chat

# Require authentication
get_auth_manager().require_auth()


import os
from agents.cover_agent import genera_suggerimenti_cover
from utils.config import config


st.markdown(
    """

    <h1 style=" text-align:center;">Step 10: La Cover Perfetta</h1>
    <p style="text-align:center;">
        <PERSON>vi consigli grafici personalizzati per una copertina attraente, mirata e professionale.
    </p>

""",
    unsafe_allow_html=True,
)


# Progress bar function
def show_progress():
    """Display progress bar"""
    st.progress(
        min(st.session_state.get("current_step", 10) / 12, 1.0),
        text=f"{int(st.session_state.get('current_step', 10) / 12 * 100)}% completato"
    )

show_progress()

def validate_prerequisites():
    """Check prerequisites only if moving forward"""
    required_keys = [
        "argomento_keyword",
        "buyer_persona_generata",
        "titolo_scelto",
    ]
    last_completed = st.session_state.get("last_step_completed", 0)

    # Only validate if we're moving forward (not backward navigation)
    if last_completed < 10:  # Step 10 is the current step
        missing = [k for k in required_keys if k not in st.session_state]
        if missing:
            st.warning(f"⚠️ Completa prima gli step precedenti: {', '.join(missing)}.")
            st.stop()

# Run validation
validate_prerequisites()


# Selettore lingua
LINGUE = {
    "it": "italiano",
    "en": "inglese",
    "es": "spagnolo",
    "fr": "francese",
    "de": "tedesco",
}

# Language selector
st.session_state["lingua_target"] = st.selectbox(
    "Lingua del report di copertina",
    list(LINGUE.keys()),
    index=list(LINGUE.keys()).index(st.session_state.get("lingua_target", "it")),
    key="cover_language_selector"
)


# Form personalizzazione
with st.form("form_cover"):
    st.subheader("🎯 Personalizza la richiesta (opzionale)")
    copertine_preferite = st.text_area(
        "Hai esempi o ispirazioni da copertine viste su Amazon?"
    )
    formato = st.selectbox(
        "Formato preferito", ["16:9", "5:8", "6x9", "Quadrato", "Non definito"]
    )
    conferma = st.form_submit_button("✅ Conferma")


if st.button("✨ Genera Suggerimenti Cover (5 crediti)", type="primary"):
    # Create prompt inside button handler
    prompt = f"""
Sei il miglior art director e grafico editoriale al mondo specializzato in copertine di libri per Amazon KDP che vendono milioni di copie.
Suggerisci una cover perfetta per un libro con queste caratteristiche:
- Argomento: {st.session_state['argomento_keyword']}
- Buyer persona: {st.session_state['buyer_persona_generata']}
- Titolo: {st.session_state['titolo_scelto']}
- Sottotitolo: {st.session_state['sottotitolo_scelto']}
- Marketplace: {st.session_state.get('marketplace', 'IT')}
- Formato preferito: {formato}
- Ispirazioni: {copertine_preferite if copertine_preferite else 'Nessuna indicazione'}

Linee guida:
- Descrivi lo stile grafico ideale (colori, font, immagini, elementi visivi)
- Indica cosa evitare assolutamente (errori comuni, cliché, scarsa leggibilità)
- Suggerisci almeno 2 possibili varianti di copertina (con descrizione)
- Se possibile, includi riferimenti a copertine bestseller simili (link Amazon)
- IMPORTANTE: DEVI ASSOLUTAMENTE SCRIVERE IN LINGUA {LINGUE.get(st.session_state["lingua_target"])}, in modo chiaro e professionale, usando elenchi puntati e titoli in grassetto
"""



    # Check credits before generation
    if get_user_credits() < 5:
        st.error("❌ Crediti insufficienti! La generazione di suggerimenti cover richiede 5 crediti.")
        st.info("💳 Puoi acquistare più crediti dalla dashboard.")
        st.stop()
    with st.spinner("Analisi grafica e suggerimenti in corso..."):
        try:
            risultato = genera_suggerimenti_cover(
                dati={
                    "Argomento": st.session_state["argomento_keyword"],
                    "Buyer Persona": st.session_state["buyer_persona_generata"],
                    "Titolo": st.session_state["titolo_scelto"],
                    "Sottotitolo": st.session_state["sottotitolo_scelto"],

                },
                prompt=prompt,
                openai_key=config.OPENAI_API_KEY,
                lingua=LINGUE.get(st.session_state['lingua_target']) #type: ignore
            )
            st.session_state["cover_suggerita"] = risultato

            # Deduct credits for successful generation
            user_id = get_user_id()
            get_auth_manager().deduct_credits(user_id, "content_generation", 5, description="Cover suggestions generation")

            # Save step 10 data
            current_project_id = st.session_state.get("current_project_id")
            if current_project_id:
                step_data = {
                    "cover_suggerita": risultato
                }
                user_id = get_user_id()
                get_auth_manager().save_step_data(user_id, current_project_id, 10, step_data)

            st.success("✅ Report grafico generato con successo!")
        except Exception as e:
            st.error(f"❌ Errore durante la generazione: {str(e)}")

if "cover_suggerita" in st.session_state:
    st.markdown("---")
    st.subheader("🖼️ Report: La Cover Perfetta")

    # Display the full report
    cover_content = st.session_state["cover_suggerita"]
    st.markdown(cover_content, unsafe_allow_html=True)

    # Parse and display cover ideas for selection
    st.markdown("---")
    st.subheader("🎨 Seleziona l'idea cover da realizzare")

    # Extract cover ideas from the generated content
    import re

    # Try to extract the three cover ideas
    cover_ideas = []

    # Multiple patterns to match different formatting styles
    patterns = [
        r'(?:Idea Cover #\d+|Cover Idea #\d+|Opzione Cover #\d+|Cover #\d+|##\s*\d+\.?|###\s*\d+\.?|\d+\.\s*(?:Idea|Cover|Opzione))',
        r'(?:Prima idea|Seconda idea|Terza idea|Idea 1|Idea 2|Idea 3)',
        r'(?:Variante \d+|Opzione \d+|Concept \d+)'
    ]

    # Try each pattern
    for pattern in patterns:
        sections = re.split(f'(?={pattern})', cover_content, flags=re.IGNORECASE)
        if len(sections) > 3:
            # Found a pattern that splits into multiple sections
            for i, section in enumerate(sections[1:4]):
                if section.strip():
                    cover_ideas.append(section.strip())
            break

    # If no pattern worked, split by double newlines and take substantial paragraphs
    if len(cover_ideas) < 3:
        cover_ideas = []
        paragraphs = cover_content.split('\n\n')
        for para in paragraphs:
            # Only include paragraphs with substantial content (more than 100 chars)
            if len(para.strip()) > 100 and len(cover_ideas) < 3:
                cover_ideas.append(para.strip())

    # If still not enough ideas, create 3 sections from the content
    if len(cover_ideas) < 3:
        # Split content into roughly equal parts
        content_lines = cover_content.split('\n')
        lines_per_idea = len(content_lines) // 3
        cover_ideas = []
        for i in range(3):
            start_idx = i * lines_per_idea
            end_idx = (i + 1) * lines_per_idea if i < 2 else len(content_lines)
            idea_text = '\n'.join(content_lines[start_idx:end_idx]).strip()
            if idea_text:
                cover_ideas.append(f"Idea #{i+1}:\n{idea_text}")

    # Ensure we have exactly 3 ideas
    while len(cover_ideas) < 3:
        cover_ideas.append(f"Idea #{len(cover_ideas)+1}: (Contenuto non disponibile)")

    # Display all three ideas first
    st.markdown("**📖 Ecco le tre idee cover generate:**")

    for i, idea in enumerate(cover_ideas[:3]):
        with st.expander(f"💡 Idea #{i+1}", expanded=(i==0)):
            st.markdown(idea)

    # Radio button selection
    st.markdown("---")
    selected_idea = st.radio(
        "**Quale idea vuoi realizzare?**",
        options=[0, 1, 2],
        format_func=lambda x: f"Idea #{x+1}",
        key="selected_cover_idea",
        horizontal=True
    )

    # Highlight the selected idea
    st.success(f"✅ Hai selezionato: **Idea #{selected_idea + 1}**")

    # Add regenerate option
    st.markdown("---")
    if st.button("🔄 Rigenera Suggerimenti", use_container_width=True):
        st.session_state.pop("cover_suggerita", None)
        st.session_state.pop("selected_cover_idea", None)
        st.rerun()

    st.markdown("---")
    col1, col2 = st.columns(2)

    with col1:
        if st.button("⬅️ Indietro", use_container_width=True):
            st.session_state["current_step"] = 9
            st.switch_page("pages/step9_descrizione_amazon.py")

    with col2:
        if st.button("✅ Procedi con l'idea selezionata", type="primary", use_container_width=True):
            # Save the selected cover idea
            st.session_state["selected_cover_idea_content"] = cover_ideas[selected_idea]
            st.session_state["selected_cover_idea_index"] = selected_idea

            # Ensure data is saved before proceeding
            current_project_id = st.session_state.get("current_project_id")
            if current_project_id:
                step_data = {
                    "cover_suggerita": st.session_state.get("cover_suggerita", ""),
                    "selected_cover_idea": cover_ideas[selected_idea],
                    "selected_cover_idea_index": selected_idea
                }
                user_id = get_user_id()
                result = get_auth_manager().save_step_data(user_id, current_project_id, 10, step_data)
                if result.get("success"):
                    st.session_state["step10_saved"] = True

            st.session_state["current_step"] = 10.5
            st.switch_page("pages/step10b_design_cover.py")

# Show help chat for step10
show_help_chat("step10_cover_perfetta")

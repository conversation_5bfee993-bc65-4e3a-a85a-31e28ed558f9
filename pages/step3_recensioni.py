from utils.auth import get_auth_manager
import streamlit as st
from utils.credits import get_user_credits, get_user_id
from components.help_chat import show_help_chat

# Require authentication
get_auth_manager().require_auth()


from sys import stdin
import streamlit as st
from openai import OpenAI
import os
import re
from typing import List, Dict, Any
from pydantic import BaseModel
from utils.config import config




class AmazonReview(BaseModel):
    reviewId: str
    text: str
    rating: str
    title: str
    userName: str
    date: str
    verified: bool = False


class ReviewsResponse(BaseModel):
    asin: str
    productTitle: str
    countReviews: int
    productRating: str
    reviews: List[AmazonReview]


def extract_asin(url: str) -> str | None:
    """Estrae ASIN dall'URL Amazon"""
    patterns = [
        r"/dp/([A-Z0-9]{10})",
        r"/gp/product/([A-Z0-9]{10})",
        r"asin=([A-Z0-9]{10})",
        r"/([A-Z0-9]{10})/",
    ]

    for pattern in patterns:
        match = re.search(pattern, url)
        if match:
            return match.group(1)
    return None


def get_amazon_urls_from_step2() -> List[str]:
    """Ottiene gli URL Amazon direttamente dai dati strutturati di Step 2"""
    amazon_books_data = st.session_state.get("amazon_books_data", [])

    if not amazon_books_data:
        return []

    urls = [
        book["url"]
        for book in amazon_books_data
        if book.get("url") and book["url"] != "#"
    ]

    return urls[:5]





def deserialize_amazon_reviews(api_response: Dict[str, Any]) -> List[Dict[str, str]]:
    """
    Deserializza la risposta dell'API Amazon e filtra recensioni 1-3 stelle
    """
    if not api_response or "reviews" not in api_response:
        return []

    negative_reviews = []

    for review in api_response.get("reviews", []):
        # Estrai rating numerico
        rating_text = review.get("rating", "")
        rating_num = extract_rating_number(rating_text)

        # Filtra solo recensioni 1-3 stelle
        if rating_num and 1 <= rating_num <= 3:
            negative_reviews.append(
                {
                    "id": review.get("reviewId", ""),
                    "text": review.get("text", ""),
                    "rating": rating_text,
                    "title": review.get("title", ""),
                    "author": review.get("userName", ""),
                    "date": review.get("date", ""),
                    "verified": review.get("verified", False),
                    "stars": rating_num,
                }
            )

    return negative_reviews


def extract_rating_number(rating_text: str) -> int:
    """Estrae il numero di stelle dal testo del rating"""
    if not rating_text:
        return 0

    # Pattern per estrarre numero di stelle
    patterns = [
        r"(\d+)[,.]0 out of 5 stars",  # "1.0 out of 5 stars"
        r"(\d+) out of 5 stars",       # "1 out of 5 stars"
        r"(\d+)[,.]0 su 5",            # Italian format
        r"(\d+) su 5",                 # Italian format
        r"(\d+)[,.]0 stars",           # Short format
        r"(\d+) stars"                 # Short format
    ]

    for pattern in patterns:
        match = re.search(pattern, rating_text)
        if match:
            return int(match.group(1))

    return 0




def fetch_amazon_reviews_api(asin: str, domain_code: str) -> Dict[str, Any]:
    """
    Chiama l'API reale per ottenere recensioni Amazon
    """
    import requests


    # Configura l'API endpoint (sostituisci con la tua API reale)
    api_url = "https://axesso-axesso-amazon-data-service-v1.p.rapidapi.com/amz/amazon-lookup-reviews"

    headers = {
        "x-rapidapi-key": config.AXESSO_KEY,
        "x-rapidapi-host": "axesso-axesso-amazon-data-service-v1.p.rapidapi.com",
    }

    querystring = {
        "domainCode": domain_code,
        "asin": asin,
        "sortBy": "recent",
        "filters": "filterByStar=one_star;two_star;three_star",
    }

    try:
        response = requests.get(
            api_url, headers=headers, params=querystring, timeout=30
        )
        response.raise_for_status()
        return response.json()

    except requests.exceptions.RequestException as e:
        st.error(f"Errore API: {str(e)}")
        return {
            "responseStatus": "ERROR",
            "responseMessage": f"API call failed: {str(e)}",
            "asin": asin,
            "reviews": [],
        }


def scan_amazon_reviews_with_api(
    amazon_urls: List[str],
    domain_code: str #type: ignore
) -> List[Dict[str, Any]]:
    """
    Scansiona le recensioni Amazon usando l'API diretta
    """
    if not amazon_urls:
        st.warning("Nessun URL Amazon trovato dai risultati precedenti.")
        return []


    reviews_data = []

    for i, url in enumerate(amazon_urls):
        st.info(f"📘 Scansionando recensioni dal libro {i+1}/{len(amazon_urls)}...")

        # Estrai ASIN dall'URL
        asin = extract_asin(url)
        if not asin:
            st.warning(f"ASIN non trovato in URL: {url}")
            continue

        st.info(f"🔍 Analizzando recensioni per ASIN: {asin}")

        try:

            api_response = fetch_amazon_reviews_api(asin, domain_code)

            # Deserializza e filtra recensioni negative
            negative_reviews = deserialize_amazon_reviews(api_response)

            if not negative_reviews:
                st.warning(f"Nessuna recensione negativa trovata per ASIN {asin}")
                continue

            # Formatta le recensioni per l'analisi AI
            formatted_reviews = []
            for review in negative_reviews:
                formatted_review = (
                    f"⭐ {review['stars']} stelle - {review['title']}\n"
                    f"Autore: {review['author']} ({review['date']})\n"
                    f"Testo: {review['text']}\n"
                    f"Verificato: {'Sì' if review['verified'] else 'No'}\n"
                    f"---"
                )
                formatted_reviews.append(formatted_review)

            reviews_data.append(
                {
                    "url": url,
                    "asin": asin,
                    "product_title": api_response.get(
                        "productTitle", "Titolo sconosciuto"
                    ),
                    "total_reviews": api_response.get("countReviews", 0),
                    "product_rating": api_response.get("productRating", "N/A"),
                    "negative_reviews_count": len(negative_reviews),
                    "reviews": "\n\n".join(formatted_reviews),
                    "source": f"ASIN {asin}",
                    "raw_reviews": negative_reviews,
                }
            )

            st.success(
                f"✅ Trovate {len(negative_reviews)} recensioni negative per {asin}"
            )

        except Exception as e:
            st.error(f"❌ Errore per {url}: {str(e)}")
            continue

    return reviews_data


def format_reviews_for_ai(reviews_data: List[Dict[str, Any]]) -> List[str]:
    """
    Formatta le recensioni per l'analisi AI
    """
    formatted_texts = []

    for book_data in reviews_data:
        book_summary = (
            f"{book_data['product_title']} (ASIN: {book_data['asin']})\n"
            f"Rating prodotto: {book_data['product_rating']}\n"
            f"Totale recensioni: {book_data['total_reviews']}\n"
            f"Recensioni negative analizzate: {book_data['negative_reviews_count']}\n\n"
            f"RECENSIONI NEGATIVE:\n{book_data['reviews']}"
        )
        formatted_texts.append(book_summary)

    return formatted_texts


def analyze_reviews_with_openai(recensioni_valide):
    """
    Analizza le recensioni usando OpenAI (mantiene la logica originale)
    """
    openai_key = config.OPENAI_API_KEY
    if not openai_key:
        st.error("❗ Chiave OpenAI mancante.")
        return

    # Check credits before analysis
    current_credits = get_user_credits()

    if current_credits < 10:
        st.error("❌ Crediti insufficienti! L'analisi delle recensioni richiede 10 crediti.")
        st.info("💳 Puoi acquistare più crediti dalla dashboard.")
        return

    prompt = (
        "Analizza le seguenti recensioni negative di libri simili e riassumi:\n"
        "- Critiche frequenti\n- Aspettative non soddisfatte\n- Errori da evitare\n"
        "Rispondi in elenco puntato. Se emergono link utili, inseriscili in formato [testo](url).\n\n"
        + "\n\n".join(recensioni_valide)
    )

    try:
        with st.spinner("Analisi AI in corso..."):
            client = OpenAI(api_key=openai_key)
            response = client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {
                        "role": "system",
                        "content": "Sei un editor professionale esperto in pubblicazioni Amazon KDP.",
                    },
                    {"role": "user", "content": prompt},
                ],
                temperature=0.6,
                max_tokens=1500,
            )
            risultato = response.choices[0].message.content
            st.session_state["report_recensioni"] = risultato
            # Store the result in the variable expected by step4
            st.session_state["problemi_recensioni"] = risultato
            # Also store as recensioni_analizzate for step6 compatibility
            st.session_state["recensioni_analizzate"] = risultato

            # Deduct credits for successful analysis
            user_id = get_user_id()
            get_auth_manager().deduct_credits(user_id, "content_generation", 10, description="Reviews analysis with OpenAI")  #type: ignore

            # Save step 3 data
            current_project_id = st.session_state.get("current_project_id")
            if current_project_id:
                step_data = {
                    "recensioni_inserite": st.session_state.get("recensioni_inserite", []),
                    "report_recensioni": risultato,
                    "problemi_recensioni": risultato,
                    "recensioni_analizzate": risultato
                }
                user_id = get_user_id()
                get_auth_manager().save_step_data(user_id, current_project_id, 3, step_data)

    except Exception as e:
        st.error(f"❌ Errore durante l'analisi AI: {e}")



# Progress bar
def show_progress():
    """Display progress bar"""
    st.progress(3/12, text=f"{int(3 / 11 * 100)}% completato")

show_progress()

def get_marketplace_domain():
    """Get marketplace domain from session state"""
    marketplace = st.session_state.get("marketplace")
    marketplace_to_domain = {
        "IT": "it",
        "US": "com",
        "UK": "co.uk",
        "DE": "de",
        "FR": "fr",
        "ES": "es",
    }
    # Convert marketplace to correct domain code
    return marketplace_to_domain.get(marketplace.upper()) if marketplace else "it"

st.markdown(
    """
    <h1 style=' text-align:center;'>Step 3: Analisi Recensioni Automatica</h1>
    <h3 style='text-align:center;'>Scansione automatica delle recensioni Amazon per identificare criticità, problemi, bisogni, emozioni.</h3>
""",
    unsafe_allow_html=True,
)

# Inizializza le variabili di sessione (mantiene compatibilità)
if "recensioni_inserite" not in st.session_state:
    st.session_state["recensioni_inserite"] = []
if "recensioni_analizzate" not in st.session_state:
    # Initialize from saved data if available, otherwise empty string to match report format
    st.session_state["recensioni_analizzate"] = st.session_state.get("problemi_recensioni", "")

# Verifica prerequisiti
if not st.session_state.get("amazon_books_data"):
    st.warning(
        "⚠️ Completa prima lo Step 2 per ottenere i libri Amazon da analizzare."
    )
    st.error("💡 Assicurati di aver eseguito la ricerca Amazon nello Step 2.")
    if st.button("⬅️ Torna allo Step 2"):
        st.switch_page("pages/step2_argomento_keyword.py")
    st.stop()

# Estrai URL Amazon dalla sessione
amazon_urls = get_amazon_urls_from_step2()

st.markdown("### 🔍 URL Amazon trovati:")
if amazon_urls:
    for i, url in enumerate(amazon_urls, 1):
        st.markdown(f"{i}. [{url}]({url})")
else:
    st.warning("Nessun URL Amazon valido trovato nei risultati precedenti.")
    st.error(
        "Prova a tornare allo Step 2 e assicurati che la ricerca Amazon abbia prodotto risultati con URL validi."
    )



# Pulsanti di azione
col1, col2 = st.columns(
    [
        2,
        1,
    ]
)
with col1:
    # Scansione automatica
    scan_reviews = st.button(
        "🔍 Scansiona Recensioni Automaticamente (10 crediti)",
        type="primary",
        disabled=not amazon_urls,
    )


with col2:
    manual_mode = st.button("✏️ Modalità Manuale")

# Initialize manual mode state
if "manual_mode_open" not in st.session_state:
    st.session_state["manual_mode_open"] = False

# Toggle manual mode state when button is pressed
if manual_mode:
    st.session_state["manual_mode_open"] = not st.session_state["manual_mode_open"]

# Modalità manuale (fallback)
if st.session_state["manual_mode_open"]:
    st.markdown("### ✏️ Inserimento Manuale Recensioni")
    with st.form("form_recensioni_manual"):
        cols = st.columns(3)

        def recensioni_colonna(stelle, col):
            col.subheader(f"{stelle} ⭐")
            return [
                col.text_area(
                    f"Recensione {stelle}.{i+1}", key=f"rec_manual_{stelle}_{i+1}"
                )
                for i in range(3)
            ]

        rec_1 = recensioni_colonna(1, cols[0])
        rec_2 = recensioni_colonna(2, cols[1])
        rec_3 = recensioni_colonna(3, cols[2])

        analizza_manual = st.form_submit_button("📊 Analizza Recensioni Manuali")

    if analizza_manual:
        tutte = rec_1 + rec_2 + rec_3
        recensioni_valide = [r.strip() for r in tutte if r.strip()]
        if not recensioni_valide:
            st.warning("⚠️ Inserisci almeno una recensione valida.")
            st.stop()

        st.session_state["recensioni_inserite"] = recensioni_valide
        analyze_reviews_with_openai(recensioni_valide)


if scan_reviews and amazon_urls:
    with st.spinner("🔍 Scansionando recensioni Amazon..."):
        reviews_data = scan_amazon_reviews_with_api(amazon_urls, get_marketplace_domain()) #type: ignore

        if reviews_data:
            # Formatta le recensioni per l'analisi AI
            formatted_reviews = format_reviews_for_ai(reviews_data)
            combined_reviews = "\n\n".join(formatted_reviews)

            st.session_state["recensioni_inserite"] = [combined_reviews]

            # Analizza con OpenAI
            analyze_reviews_with_openai([combined_reviews])

            # Mostra statistiche
            total_negative_reviews = sum(
                book["negative_reviews_count"] for book in reviews_data
            )
            st.info(
                f"📊 Analizzate {len(reviews_data)} libri con {total_negative_reviews} recensioni negative totali"
            )

            # Mostra i risultati grezzi
            with st.expander("📄 Recensioni estratte (dati strutturati)"):
                for book_data in reviews_data:
                    st.markdown(
                        f"**📚 {book_data['product_title']} (ASIN: {book_data['asin']})**"
                    )
                    st.markdown(f"- Rating: {book_data['product_rating']}")
                    st.markdown(
                        f"- Totale recensioni: {book_data['total_reviews']}"
                    )
                    st.markdown(
                        f"- Recensioni negative: {book_data['negative_reviews_count']}"
                    )

                    # Mostra alcune recensioni negative
                    if book_data["raw_reviews"]:
                        st.markdown("**Esempi di recensioni negative:**")
                        for review in book_data["raw_reviews"][
                            :3
                        ]:  # Mostra solo le prime 3
                            st.markdown(
                                f"⭐ {review['stars']} - *{review['title']}*"
                            )
                            st.markdown(f"  \"{review['text'][:100]}...\"")
                    st.markdown("---")
        else:
            st.error(
                "❌ Impossibile estrarre recensioni. Prova la modalità manuale."
            )

# Mostra risultati se disponibili
if st.session_state.get("report_recensioni"):
    st.markdown("### 🤖 Risultato dell'analisi KDP:")
    st.markdown(st.session_state["report_recensioni"], unsafe_allow_html=True)

    # Add regenerate option
    if st.button("🔄 Rigenera Analisi (10 crediti)", use_container_width=True):
        st.session_state.pop("report_recensioni", None)
        st.session_state.pop("problemi_recensioni", None)
        st.rerun()

    st.markdown("---")
    col1, col2 = st.columns(2)

    with col1:
        if st.button("⬅️ Indietro", use_container_width=True):
            st.session_state["current_step"] = 2
            st.switch_page("pages/step2_argomento_keyword.py")

    with col2:
        if st.button("✅ Conferma e continua", type="primary", use_container_width=True):
            # Ensure data is saved before proceeding
            current_project_id = st.session_state.get("current_project_id")
            if current_project_id and not st.session_state.get("step3_saved"):
                step_data = {
                    "recensioni_inserite": st.session_state.get("recensioni_inserite", []),
                    "report_recensioni": st.session_state.get("report_recensioni", ""),
                    "problemi_recensioni": st.session_state.get("problemi_recensioni", ""),
                    "recensioni_analizzate": st.session_state.get("recensioni_analizzate", "")
                }
                user_id = get_user_id()
                result = get_auth_manager().save_step_data(user_id, current_project_id, 3, step_data)
                if result.get("success"):
                    st.session_state["step3_saved"] = True

            st.session_state["current_step"] = 4
            st.switch_page("pages/step4_buyer_persona.py")

# Show help chat for step3
show_help_chat("step3_recensioni")

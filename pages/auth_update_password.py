import streamlit as st
from utils.auth import get_auth_manager
from supabase import Client


# Custom CSS
st.markdown("""
<style>
    .main {
        padding-top: 2rem;
    }
    .stForm {
        background-color: #f8f9fa;
        padding: 2rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    h1 {
        color: #1e3a8a;
        text-align: center;
        margin-bottom: 2rem;
    }
</style>
""", unsafe_allow_html=True)

# Get parameters from query string
query_params = st.query_params
access_token = query_params.get("access_token", None)
token_type = query_params.get("type", None)
refresh_token = query_params.get("refresh_token", None)

# Debug what we got
"""
st.write("🔍 Extracted values:")
st.write(f"- access_token: {access_token[:20] + '...' if access_token else 'None'}")
st.write(f"- type: {token_type}")
st.write(f"- refresh_token: {refresh_token[:10] + '...' if refresh_token else 'None'}")

# Also debug the raw query params
st.write("🔍 Raw query params:", dict(st.query_params))
"""
# If not a recovery flow, redirect to login
if not access_token or token_type != "recovery":
    st.error("Il link di reset della password è invalido o scaduto.")
    st.info("Per favore, richiedi un nuovo link di reset della password.")
    if st.button("Accedi"):
        st.switch_page("pages/auth_login.py")
    st.stop()

# Header
st.markdown(
    """
    <h1 style='text-align: center; color: #222; font-size: 3em;'>🔐 Crea una nuova Password</h1>
    <h3 style='text-align: center; color: #444;'>Scegli una password sicura per il tuo account</h3>
    """,
    unsafe_allow_html=True
)

# Password update form
with st.container():
    with st.form("update_password_form"):
        new_password = st.text_input("Nuova Password", type="password", placeholder="Inserisci nuova password")
        confirm_password = st.text_input("Confirma la Password", type="password", placeholder="Conferma nuova password")

        # Password requirements
        st.markdown("""
        **La Password deve:**
        - Essere almeno 8 caratteri lunga
        - Includere lettere maiuscole e minuscole
        - Includere almeno un numero
        """)

        update_button = st.form_submit_button("🔐 Update Password", type="primary", use_container_width=True)

    # Handle password update
    if update_button:
        if not new_password or not confirm_password:
            st.error("Perfavore inserisci entrambe le password.")
        elif new_password != confirm_password:
            st.error("Le password non corrispondono. Perfavore riprova.")
        elif len(new_password) < 8:
            st.error("La password deve essere almeno 8 caratteri lunga.")
        else:
            try:
                # Update password using the recovery token
                auth_mgr = get_auth_manager()
                supabase_client: Client = auth_mgr.conn

                # Set the session with the recovery token
                supabase_client.auth.set_session(access_token, refresh_token if refresh_token else "")

                # Update the password
                response = supabase_client.auth.update_user(
                    attributes={"password": new_password}
                )

                if response:
                    st.success("✅ Password aggiornata con successo!")
                    st.info("Ti sto reindirizzando alla pagina di accesso...")

                    # Clear URL parameters
                    st.query_params.clear()

                    # Wait a moment then redirect
                    import time
                    time.sleep(2)
                    st.switch_page("pages/auth_login.py")
                else:
                    st.error("Non è stato possibile aggiornare la password. Perfavore riprova.")

            except Exception as e:
                st.error(f"Errore aggiornando password: {str(e)}")
                st.info("Il link di reset potrebbe essere scaduto. Per favore richiedi un nuovo link.")

# Back to login link
st.markdown("---")
col1, col2, col3 = st.columns([1, 2, 1])
with col2:
    if st.button("⬅️ Torna alla pagina di accesso", use_container_width=True):
        st.switch_page("pages/auth_login.py")

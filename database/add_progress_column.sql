-- Add missing progress_percentage column to projects table
ALTER TABLE public.projects 
ADD COLUMN IF NOT EXISTS progress_percentage INTEGER DEFAULT 0 
CHECK (progress_percentage >= 0 AND progress_percentage <= 100);

-- Update existing projects to have default progress
UPDATE public.projects 
SET progress_percentage = 0 
WHERE progress_percentage IS NULL;

-- Add missing last_step_completed column if needed
ALTER TABLE public.projects 
ADD COLUMN IF NOT EXISTS last_step_completed INTEGER DEFAULT 0;

-- Update existing projects to have default last step
UPDATE public.projects 
SET last_step_completed = 0 
WHERE last_step_completed IS NULL;

-- Verify the columns exist
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'projects' 
AND table_schema = 'public'
AND column_name IN ('progress_percentage', 'last_step_completed');
from utils.auth import get_auth_manager
import streamlit as st
import os

from agents.buyer_persona_agent import genera_buyer_persona
from utils.config import config
from utils.credits import get_user_id, get_user_credits
from utils.fonti_web import cerca_fonti
from components.help_chat import show_help_chat


# Require authentication
get_auth_manager().require_auth()


# Progress bar function
def show_progress():
    """Display progress bar"""
    current = st.session_state.get("current_step", 4)
    st.progress(4/12, text=f"{int(current / 11 * 100)}% completato")

show_progress()
st.markdown(
    """
    <h1 style=' text-align:center;'>Step 4: Buyer Persona</h1>
    <h3 style='text-align:center;'>Utilizza il topic, le ricerche, l'analisi delle recensioni effettuate per creare un profilo dettagliato della buyer persona ideale per un libro.</h3>
""",
    unsafe_allow_html=True,
)

# Check prerequisites but don't block navigation
missing_prerequisites = []
if "argomento_keyword" not in st.session_state:
    missing_prerequisites.append("Step 2: Argomento & Keyword")
if "problemi_recensioni" not in st.session_state:
    missing_prerequisites.append("Step 3: Analisi Recensioni")

if missing_prerequisites:
    st.warning(f"⚠️ Completa prima: {', '.join(missing_prerequisites)}")
    prerequisites_met = False
else:
    prerequisites_met = True

if prerequisites_met and st.button("🎯 Genera Buyer Persona (8 crediti)", type="primary"):
    openai_key = config.OPENAI_API_KEY

    # Get marketplace to determine nationality
    marketplace = st.session_state.get("marketplace", "IT")
    marketplace_to_nationality = {
        "IT": "italiana (Italia)",
        "US": "americana (Stati Uniti)",
        "UK": "britannica (Regno Unito)",
        "DE": "tedesca (Germania)",
        "FR": "francese (Francia)",
        "ES": "spagnola (Spagna)"
    }
    nationality = marketplace_to_nationality.get(marketplace, "italiana")

    # Create prompt inside button handler to avoid module-level session state access
    prompt = f"""
    Act as a world-class strategic marketing analyst and consumer psychologist. Your expertise lies in synthesizing quantitative data and qualitative insights to build deeply human, actionable buyer personas. Your goal is not just to list data, but to tell the story of a person.

    # TASK
    Based on this informations,  generate a single, flawless, and comprehensive buyer persona for a specific product/service. This persona must be the "north star" for all subsequent marketing, sales, and product development strategies. The final output must be a professional, actionable document that feels like it was crafted by an expert human analyst.

    # IMPORTANT: The buyer persona MUST be {nationality} and represent a typical consumer from the Amazon {marketplace} marketplace.

    # CONTEXT
    The product/service for which we are creating the persona is:
    •⁠  ⁠*Ebook* su {st.session_state['argomento_keyword']} che ha recensito malamente alcuni ebook di nostri compoetior: {st.session_state['problemi_recensioni']}
    •⁠  ⁠*Core Value Proposition:* [INSERISCI QUI IL VALORE PRINCIPALE CHE OFFRE. Esempio: "Insegna a chiunque, anche senza esperienza, a coltivare erbe aromatiche in piccoli spazi come un balcone, per migliorare il proprio benessere."]
    •⁠  ⁠*Target Audience (Initial Idea):* [INSERISCI QUI UNA BREVE IDEA DEL TUO PUBBLICO TARGET. Esempio: "Professionisti che vivono in città, stressati, che cercano un hobby per rilassarsi e connettersi con la natura."]

    # PERSONA STRUCTURE & ANALYSIS POINTS
    Generate the persona following this exact structure. For each point, do not just state the fact, but elaborate on its implications.

    ---

    ### *[DAI UN NOME ALLA PERSONA, es. "Laura, l'Aspirante Coltivatrice Urbana"]*

    *1. SINTESI E CITAZIONE*
    •⁠  ⁠*Bio in Breve:* Scrivi una bio di 2-3 frasi che riassuma chi è questa persona, cosa fa e cosa desidera. Deve essere un'istantanea vivida della sua vita.
    •⁠  ⁠*Motto o Citazione:* Crea una frase che questa persona direbbe e che cattura la sua frustrazione o il suo obiettivo principale. Esempio: "Passo tutto il giorno davanti a uno schermo, vorrei solo creare qualcosa di reale e sano con le mie mani, ma non so da dove cominciare."

    *2. DATI DEMOGRAFICI E CONTESTO DI VITA*
    •⁠  ⁠*Età:* [INSERISCI FASCIA D'ETÀ, es. 30-40 anni]
    •⁠  ⁠*Genere:* [INSERISCI GENERE, se rilevante]
    •⁠  ⁠*Luogo:* [INSERISCI AREA GEOGRAFICA, es. "Residente in una grande città del Nord Italia (es. Milano, Torino)"]
    •⁠  ⁠*Situazione Familiare:* [INSERISCI SITUAZIONE, es. "Single o in coppia, senza figli"]
    •⁠  ⁠*Istruzione:* [INSERISCI LIVELLO, es. "Laurea magistrale in discipline umanistiche o marketing/comunicazione"]
    •⁠  ⁠*Lavoro e Carriera:* Descrivi il suo ruolo, settore e anzianità. Spiega cosa implica il suo lavoro a livello di stress e routine quotidiana. [INSERISCI DETTAGLI, es. "Responsabile Marketing in un'azienda tech. Lavoro intellettuale, scadenze strette, molte ore al computer."]
    •⁠  ⁠*Reddito:* [INSERISCI FASCIA, es. "Reddito medio-alto, che le permette di investire in hobby e benessere personale."]

    *3. COMPORTAMENTI E INTERESSI (Lo Stile di Vita)*
    •⁠  ⁠*Hobby e Passioni:* Elenca i suoi interessi, spiegando perché la attraggono. [INSERISCI HOBBY, es. "Yoga, lettura di saggi, cucina salutare, interesse per la sostenibilità. Cerca attività che bilancino la sua vita digitale."]
    •⁠  ⁠*Fonti di Informazione e Influencer:* Dove si informa? Quali media segue per le decisioni d'acquisto? Sii specifico. [INSERISCI FONTI, es. "Legge blog di benessere e crescita personale, segue su Instagram influencer di 'slow living' e design d'interni, ascolta podcast."]
    •⁠  ⁠*Canali Social Preferiti:* Quali piattaforme usa e come le usa? [INSERISCI CANALI, es. "Usa Instagram per ispirazione visiva (non per socializzare), LinkedIn per la carriera, Pinterest per salvare idee per progetti futuri."]
    •⁠  ⁠*Comportamento d'Acquisto:* Come prende decisioni? È impulsiva o riflessiva? Cosa la convince? [INSERISCI COMPORTAMENTO, es. "È un'acquirente riflessiva. Sensibile alla qualità e all'estetica più che al prezzo. Legge recensioni dettagliate e cerca prove di autenticità prima di acquistare online."]

    *4. OBIETTIVI E MOTIVAZIONI (Cosa la Spinge)*
    •⁠  ⁠*Obiettivi Professionali:* Cosa vuole raggiungere nel suo lavoro?
    •⁠  ⁠*Obiettivi Personali:* Cosa vuole per la sua vita? (es. "Trovare un equilibrio vita-lavoro migliore", "Imparare una nuova abilità pratica").
    •⁠  ⁠*Motivazioni all'Acquisto (Jobs-to-be-Done):* Qual è il vero "lavoro" che sta cercando di "assumere" il tuo prodotto per fare? (es. "Mi serve qualcosa che mi 'assuma' per ridurre lo stress serale" o "Mi serve qualcosa che mi 'assuma' per farmi sentire più competente e produttiva").

    *5. SFIDE E PUNTI DOLOROSI (Cosa la Frena)*
    •⁠  ⁠*Frustrazioni e Ostacoli:* Quali sono i problemi concreti che le impediscono di raggiungere i suoi obiettivi? (es. "Mancanza di tempo a causa del lavoro", "Spazi limitati in casa", "Paura di sprecare soldi in un hobby che poi abbandonerà").
    •⁠  ⁠*Paure e Preoccupazioni:* Quali sono le sue ansie più profonde legate a questo ambito? (es. "Paura di fallire e non essere all'altezza ('non ho il pollice verde')", "Scetticismo verso soluzioni 'miracolose' o informazioni superficiali trovate online").




"""


    # Check credits before generation
    if get_user_credits() < 8:
        st.error("❌ Crediti insufficienti! La generazione della buyer persona richiede 8 crediti.")
        st.info("💳 Puoi acquistare più crediti dalla dashboard.")
        st.stop()

    with st.spinner("Generazione in corso..."):
        try:
            persona = genera_buyer_persona(
                dati={},
                fonti_web="",
                openai_key=openai_key,
                prompt_custom=prompt,
            )
            st.session_state["buyer_persona_generata"] = persona

            # Deduct credits for successful generation
            user_id = get_user_id()
            get_auth_manager().deduct_credits(user_id, "content_generation", 8, description="Buyer persona generation")

            # Save step 4 data
            current_project_id = st.session_state.get("current_project_id")
            if current_project_id:
                step_data = {
                    "buyer_persona_generata": persona
                }
                user_id = get_user_id()
                get_auth_manager().save_step_data(user_id, current_project_id, 4, step_data)

        except Exception as e:
            st.error(f"❌ Errore: {e}")

if "buyer_persona_generata" in st.session_state:
    st.markdown("### 👤 Buyer Persona Generata:")
    st.markdown(st.session_state["buyer_persona_generata"])

    # Add regenerate option
    if prerequisites_met and st.button("🔄 Rigenera Buyer Persona (8 crediti)", use_container_width=True):
        st.session_state.pop("buyer_persona_generata", None)
        st.rerun()

# Always show navigation at the bottom
st.markdown("---")
col1, col2 = st.columns(2)

with col1:
    if st.button("⬅️ Indietro", use_container_width=True):
        st.session_state["current_step"] = 3
        st.switch_page("pages/step3_recensioni.py")

with col2:
    # Only show continue button if buyer persona has been generated
    if "buyer_persona_generata" in st.session_state:
        if st.button("✅ Conferma e continua", type="primary", use_container_width=True):
            # Ensure data is saved before proceeding
            current_project_id = st.session_state.get("current_project_id")
            if current_project_id and not st.session_state.get("step4_saved"):
                step_data = {
                    "buyer_persona_generata": st.session_state.get("buyer_persona_generata", "")
                }
                user_id = get_user_id()
                result = get_auth_manager().save_step_data(user_id, current_project_id, 4, step_data)
                if result.get("success"):
                    st.session_state["step4_saved"] = True

            st.session_state["current_step"] = 5
            st.switch_page("pages/step5_posizionamento.py")
    else:
        st.info("📝 Genera la buyer persona per continuare al prossimo step")

# Show help chat for step4
show_help_chat("step4_buyer_persona")

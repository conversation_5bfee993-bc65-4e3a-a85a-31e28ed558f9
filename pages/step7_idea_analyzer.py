from utils.auth import get_auth_manager
import streamlit as st
from utils.credits import get_user_id, get_user_credits
from components.help_chat import show_help_chat

# Require authentication
get_auth_manager().require_auth()



import os
import streamlit as st
from openai import OpenAI
from utils.config import config
import json

# Progress bar
def show_progress():
    """Display progress bar"""
    st.progress(min(7 / 12, 1.0), text=f"{int(7 / 12 * 100)}% completato")

show_progress()

st.markdown(
    """

    <h1 style="text-align:center;">Step 7: Idea Analyzer</h1>
    <h3 style="text-align:center; ">
        Analizza il tuo concept per originalità, compliance KDP e potenziali rischi prima di procedere.
    </h3>

""",
    unsafe_allow_html=True,
)

# Check prerequisites
required_data = {
    "titolo_scelto": "Step 6 - Titolo",
    "sottotitolo_scelto": "Step 6 - Sottoti<PERSON><PERSON>",
    "argomento_keyword": "Step 2 - Argomento",
    "buyer_persona_generata": "Step 4 - Buyer Persona"
}

def validate_prerequisites():
    """Check prerequisites only if moving forward"""
    last_completed = st.session_state.get("last_step_completed", 0)

    # Only validate if we're moving forward (not backward navigation)
    if last_completed < 7:  # Step 7 is the current step
        missing_steps = []
        for key, step_name in required_data.items():
            if not st.session_state.get(key):
                missing_steps.append(step_name)

        if missing_steps:
            st.warning(f"⚠️ Completa prima questi step: {', '.join(missing_steps)}")
            if st.button("⬅️ Torna allo Step 6"):
                st.switch_page("pages/step6_titolo_sottotitolo.py")
            st.stop()

# Run validation
validate_prerequisites()

# Display current book concept
st.markdown("### 📚 Concept da Analizzare")
with st.container(border=True):
    st.markdown(f"**📖 Titolo:** {st.session_state['titolo_scelto']}")
    st.markdown(f"**✨ Sottotitolo:** {st.session_state['sottotitolo_scelto']}")
    st.markdown(f"**🔑 Argomento:** {st.session_state['argomento_keyword']}")
    marketplace = st.session_state.get('marketplace', 'IT')
    st.markdown(f"**🌍 Marketplace:** Amazon.{marketplace.lower()}")

def analyze_book_idea():
    """Analyze book idea for risks, compliance, and market potential"""
    try:
        client = OpenAI(api_key=config.OPENAI_API_KEY)

        # Get book data
        title = st.session_state['titolo_scelto']
        subtitle = st.session_state['sottotitolo_scelto']
        topic = st.session_state['argomento_keyword']
        buyer_persona = st.session_state['buyer_persona_generata']
        marketplace = st.session_state.get('marketplace', 'IT')

        prompt = f"""You are an expert Amazon KDP compliance analyst and publishing consultant.

Analyze this book concept for risks, compliance issues, and market potential:

**BOOK CONCEPT:**
- Title: "{title}"
- Subtitle: "{subtitle}"
- Topic/Keyword: "{topic}"
- Target Marketplace: Amazon.{marketplace.lower()}
- Target Audience: {buyer_persona}

**ANALYSIS REQUIRED:**

1. **KDP COMPLIANCE RISKS** (Critical Priority)
   - Check for banned keywords or phrases
   - Medical/health claims that require disclaimers
   - Copyright/trademark potential conflicts
   - Adult content or sensitive topics
   - Religious or political sensitivity
   - Financial advice compliance

2. **MARKET ORIGINALITY**
   - Uniqueness vs existing titles
   - Differentiation factors
   - Oversaturated market signals
   - Competitive advantage assessment

3. **AUDIENCE RELEVANCE**
   - Title-audience alignment
   - Market demand indicators
   - Potential reach limitations

4. **RISK MITIGATION**
   - Specific recommendations for identified risks
   - Title/subtitle modification suggestions if needed
   - Content structure recommendations
   - Legal disclaimers needed

**RESPONSE FORMAT:**
You MUST respond with ONLY a valid JSON object, no additional text before or after. Use this exact structure:
{{
    "overall_risk_level": "LOW/MEDIUM/HIGH",
    "compliance_score": 85,
    "originality_score": 75,
    "market_relevance": 90,
    "critical_issues": ["list of critical problems"],
    "warnings": ["list of warnings"],
    "recommendations": ["specific actionable recommendations"],
    "title_modifications": ["suggested title improvements if needed"],
    "content_disclaimers": ["required disclaimers"],
    "competitive_advantages": ["unique selling points identified"],
    "market_insights": "market analysis summary",
    "proceed_recommendation": "PROCEED/MODIFY/RECONSIDER"
}}

IMPORTANT: Return ONLY the JSON object. No explanations, no additional text, no markdown formatting. Start with {{ and end with }}. Analyze thoroughly and be specific about Amazon KDP policies for the {marketplace} marketplace. Use Italian language for all text fields."""

        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": "You are an expert Amazon KDP compliance analyst who provides detailed risk assessments and market analysis for book concepts."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.3,
            max_tokens=2000
        )

        content = response.choices[0].message.content
        try:
            # Try to parse JSON response directly first
            analysis = json.loads(content) #type:ignore
            return analysis
        except json.JSONDecodeError:
            # Try to extract JSON from response if there's extra text
            try:
                import re
                # Look for JSON block in the response
                json_match = re.search(r'\{.*\}', content, re.DOTALL) #type: ignore
                if json_match:
                    json_str = json_match.group()
                    analysis = json.loads(json_str)
                    return analysis
                else:
                    raise json.JSONDecodeError("No JSON found", content, 0) #type: ignore
            except (json.JSONDecodeError, AttributeError):
                # Last resort: try to parse partial JSON or create structured fallback
                return {
                    "overall_risk_level": "MEDIUM",
                    "compliance_score": 75,
                    "originality_score": 70,
                    "market_relevance": 80,
                    "critical_issues": ["Analisi AI completata ma formato non standard - verifica manuale consigliata"],
                    "warnings": ["Risposta AI ricevuta ma richiede formattazione manuale"],
                    "recommendations": ["Verifica manualmente compliance KDP", "Controlla titoli simili su Amazon", "Considera i risultati nell'analisi di mercato sottostante"],
                    "title_modifications": [],
                    "content_disclaimers": ["Verifica necessità disclaimer basato sui contenuti del libro"],
                    "competitive_advantages": ["Analisi dettagliata disponibile nella sezione mercato"],
                    "market_insights": content if content else "Analisi di mercato non disponibile",
                    "proceed_recommendation": "MODIFY"
                }

    except Exception as e:
        st.error(f"Errore durante l'analisi: {str(e)}")
        return None

# Analysis section
st.markdown("### 🔍 Analisi Completa del Concept")

st.markdown("""
**🔒 Compliance KDP:**
- Termini vietati da Amazon
- Possibili violazioni copyright/trademark
- Contenuti sensibili o per adulti
- Necessità di disclaimer legali/medici

**🎯 Originalità e Mercato:**
- Unicità rispetto ai competitor
- Saturazione del mercato
- Potenziale di differenziazione
- Rilevanza per il target

**⚠️ Gestione Rischi:**
- Identificazione problematiche critiche
- Raccomandazioni specifiche
- Modifiche suggerite a titolo/sottotitolo
- Strategie di mitigazione
""")

if st.button("🔍 Analizza Concept (5 crediti)", type="primary"):
    # Check credits before analysis
    if get_user_credits() < 5:
        st.error("❌ Crediti insufficienti! L'analisi del concept richiede 5 crediti.")
        st.info("💳 Puoi acquistare più crediti dalla dashboard.")
        st.stop()

    with st.spinner("🔍 Analisi completa in corso..."):
        analysis_result = analyze_book_idea()

        if analysis_result:
            st.session_state["idea_analysis"] = analysis_result

            # Deduct credits for successful analysis
            user_id = get_user_id()
            get_auth_manager().deduct_credits(user_id, "content_generation", 5, description="Book concept analysis")

            # Save step 7 data
            current_project_id = st.session_state.get("current_project_id")
            if current_project_id:
                step_data = {
                    "idea_analysis": analysis_result,
                    "analysis_completed": True
                }
                user_id = get_user_id()
                get_auth_manager().save_step_data(user_id, current_project_id, 7, step_data)

            st.success("✅ Analisi completata!")
            st.rerun()

# Display analysis results
if st.session_state.get("idea_analysis"):
    analysis = st.session_state["idea_analysis"]

    st.markdown("---")
    st.markdown("### 📊 Risultati dell'Analisi")

    # Overall summary cards
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        risk_level = analysis.get("overall_risk_level", "MEDIUM")
        risk_color = {"LOW": "🟢", "MEDIUM": "🟡", "HIGH": "🔴"}.get(risk_level, "🟡")
        st.metric("Livello Rischio", f"{risk_color} {risk_level}")

    with col2:
        compliance = analysis.get("compliance_score", 0)
        st.metric("Compliance KDP", f"{compliance}/100")

    with col3:
        originality = analysis.get("originality_score", 0)
        st.metric("Originalità", f"{originality}/100")

    with col4:
        relevance = analysis.get("market_relevance", 0)
        st.metric("Rilevanza Mercato", f"{relevance}/100")

    # Critical issues and warnings
    if analysis.get("critical_issues"):
        st.markdown("#### 🚨 Problematiche Critiche")
        for issue in analysis["critical_issues"]:
            st.error(f"❌ {issue}")

    if analysis.get("warnings"):
        st.markdown("#### ⚠️ Avvertimenti")
        for warning in analysis["warnings"]:
            st.warning(f"⚠️ {warning}")

    # Recommendations
    if analysis.get("recommendations"):
        st.markdown("#### 💡 Raccomandazioni")
        for rec in analysis["recommendations"]:
            st.info(f"💡 {rec}")

    # Title modifications if suggested
    if analysis.get("title_modifications"):
        st.markdown("#### ✏️ Modifiche Suggerite al Titolo")
        for mod in analysis["title_modifications"]:
            st.info(f"✏️ {mod}")

    # Required disclaimers
    if analysis.get("content_disclaimers"):
        st.markdown("#### 📋 Disclaimer Necessari")
        for disclaimer in analysis["content_disclaimers"]:
            st.info(f"📋 {disclaimer}")

    # Competitive advantages
    if analysis.get("competitive_advantages"):
        st.markdown("#### 🏆 Punti di Forza Identificati")
        for advantage in analysis["competitive_advantages"]:
            st.success(f"🏆 {advantage}")

    # Market insights
    if analysis.get("market_insights"):
        st.markdown("#### 📈 Analisi di Mercato")
        st.markdown(analysis["market_insights"])

    # Final recommendation
    proceed_rec = analysis.get("proceed_recommendation", "MODIFY")
    st.markdown("#### 🎯 Raccomandazione Finale")

    if proceed_rec == "PROCEED":
        st.success("✅ **PROCEDI** - Il concept è pronto per la fase successiva!")
    elif proceed_rec == "MODIFY":
        st.warning("⚠️ **MODIFICA** - Apporta le modifiche suggerite prima di procedere.")
    else:
        st.error("🔄 **RICONSIDELA** - Il concept necessita di una revisione significativa.")

    # Add regenerate option
    if st.button("🔄 Rigenera Analisi (5 crediti)", use_container_width=True):
        st.session_state.pop("idea_analysis", None)
        st.rerun()

# Navigation
st.markdown("---")
col1, col2 = st.columns(2)

with col1:
    if st.button("⬅️ Indietro", use_container_width=True):
        st.session_state["current_step"] = 6
        st.switch_page("pages/step6_titolo_sottotitolo.py")

# Only show continue if analysis is completed
if st.session_state.get("idea_analysis"):
    with col2:
        if st.button("✅ Conferma e continua", type="primary", use_container_width=True):
            # Save current step progress
            current_project_id = st.session_state.get("current_project_id")
            if current_project_id and not st.session_state.get("step7_saved"):
                step_data = {
                    "idea_analysis": st.session_state.get("idea_analysis", {}),
                    "analysis_completed": True
                }
                user_id = get_user_id()
                result = get_auth_manager().save_step_data(user_id, current_project_id, 7, step_data)
                if result.get("success"):
                    st.session_state["step7_saved"] = True

            st.session_state["current_step"] = 8
            st.switch_page("pages/step8_indice_perfetto.py")

# Footer
st.markdown("---")
st.markdown(
    """
    <div style="text-align: center; color: #666; font-size: 0.8em;">
        <p>🔍 L'analisi preventiva ti aiuta a evitare problemi con Amazon KDP e a ottimizzare il potenziale di mercato</p>
    </div>
    """,
    unsafe_allow_html=True,
)

# Show help chat for step7
show_help_chat("step7_idea_analyzer")

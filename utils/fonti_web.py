# utils/fonti_web.py
import requests
import logging

def cerca_fonti(query: str, marketplace_code: str, serpapi_key: str, max_results: int = 3) -> list:
    if not serpapi_key:
        logging.warning("SerpApi key non fornita per cerca_fonti.")
        return ["Ricerca fonti web non possibile: chiave <PERSON> mancante."]

    gl_map = {"IT": "it", "US": "us", "UK": "uk", "DE": "de", "FR": "fr", "ES": "es"}
    gl = gl_map.get(marketplace_code.upper(), "it")
    
    params = {
        "engine": "google",
        "q": query, # Query mirata (es. "forum [argomento]", "blog [argomento] opinioni")
        "api_key": serpapi_key,
        "gl": gl,
        "hl": gl, # Lingua dei risultati
        "num": max_results * 2 # Chiedi un po' di più per filtrare
    }
    links = []
    try:
        response = requests.get("https://serpapi.com/search", params=params, timeout=15)
        response.raise_for_status()
        data = response.json()
        
        if "error" in data:
            logging.error(f"SerpApi error for query '{query}': {data['error']}")
            return [f"Errore SerpApi: {data['error']}"]

        if "organic_results" in data:
            for res in data["organic_results"]:
                if "link" in res:
                    links.append({"title": res.get("title", "N/D"), "link": res["link"], "snippet": res.get("snippet","N/D")})
                if len(links) >= max_results:
                    break
        return links if links else ["Nessuna fonte web pertinente trovata per la query specifica."]
    except requests.exceptions.Timeout:
        logging.error(f"Timeout SerpApi per query: {query}")
        return ["Timeout durante la ricerca fonti web."]
    except requests.exceptions.RequestException as e:
        logging.error(f"Errore di rete SerpApi per query '{query}': {e}")
        return [f"Errore di rete durante la ricerca fonti: {e}"]
    except Exception as e_gen:
        logging.error(f"Errore generico in cerca_fonti per query '{query}': {e_gen}", exc_info=True)
        return [f"Errore imprevisto ricerca fonti: {e_gen}"]

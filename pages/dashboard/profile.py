import streamlit as st
from utils.auth import get_auth_manager
import re
from utils.credits import get_user_credits, get_user_id

# Require authentication (restoration handled in app.py)
get_auth_manager().require_auth()

# Header
st.markdown(
    f"""
    <h1 style='text-align: center; color: #222; font-size: 2.5em;'>Profile Settings</h1>
    <h3 style='text-align: center; color: #444;'>Manage your account information</h3>
    """,
    unsafe_allow_html=True,
)

# Navigation is now handled by the main app.py sidebar

# Profile information display
st.markdown("### 📋 Account Information")

col1, col2 = st.columns([2, 1])

with col1:
    # Current profile info
    st.markdown("#### Current Profile")

    info_col1, info_col2 = st.columns(2)

    with info_col1:
        user_data = st.session_state.get("user_data", {})
        st.markdown(f"**Nome:** {user_data.get('name', 'Not set')}")
        st.markdown(f"**Email:** {user_data.get('email', 'Not available')}")
        st.markdown(f"**Piano:** {user_data.get('subscription_status', 'free').title()}")

    with info_col2:
        user_data = st.session_state.get("user_data", {})
        st.markdown(f"**Crediti:** {user_data.get('credits', 0)}")
        st.markdown(f"**Membro da:** {user_data.get('created_at', 'Unknown')}")
        onboarding_status = "✅ Completato" if user_data.get('onboarding_completed', False) else "❌ Incomplete"
        st.markdown(f"**Onboarding:** {onboarding_status}")

with col2:
    # Profile stats
    st.markdown("#### Statistiche")

    # Get additional data
    user_id = get_user_id()
    projects = get_auth_manager().get_user_projects(user_id)
    project_count = len(projects) if projects else 0
    has_vision = get_auth_manager().check_vision_exists(user_id)

    st.metric("📚 Progetti Creati", project_count)
    st.metric("🎯 Visione d'autore", "Complete" if has_vision else "Optional")
    user_data = st.session_state.get("user_data", {})
    st.metric("💳 Crediti", user_data.get('credits', 0))

st.markdown("---")

# Edit profile section
st.markdown("### ✏️ Edita il tuo Profilo")

# Profile editing form
with st.form("edit_profile_form"):
    st.markdown("#### Aggiorna le tue Informazioni")

    # Get current user profile
    user_id = get_user_id()
    current_profile = get_auth_manager().get_user_profile(user_id)
    user_data = st.session_state.get("user_data", {})

    new_name = st.text_input(
        "Full Name",
        value=current_profile.get('name', '') if current_profile else user_data.get('name', ''),
        help="Il tuo nome"
    )

    # Email is readonly (managed by Supabase Auth)
    st.text_input(
        "Email",
        value=user_data.get('email', ''),
        disabled=True,
        help="L'email non può essere modificata. Contatta il supporto se hai bisogno di aggiornarla."
    )

    # Additional profile fields
    col1, col2 = st.columns(2)

    with col1:
        # Notification preferences
        st.markdown("**Notification Preferences:**")
        email_notifications = st.checkbox(
            "Notifiche email per aggiornamenti dei progetti",
            value=True,
            help="Ricevi email quando i tuoi progetti sono pronti"
        )
        newsletter = st.checkbox(
            "Iscriviti alla newsletter KDP GENIUS",
            value=True,
            help="Tips, aggiornamenti e best practices"
        )

    with col2:
        # Privacy settings
        st.markdown("**Privacy Settings:**")
        profile_public = st.checkbox(
            "Fai pubblico il profilo",
            value=False,
            help="Consenti agli altri utenti di vedere i tuoi lavori pubblicati"
        )
        analytics = st.checkbox(
            "Consenti l'analisi di utilizzo",
            value=True,
            help="Consenti l'analisi di utilizzo"
        )

    # Form buttons
    col1, col2 = st.columns(2)
    with col1:
        save_changes = st.form_submit_button("💾 Salva le modifiche", type="primary", use_container_width=True)
    with col2:
        cancel_edit = st.form_submit_button("❌ Annulla", use_container_width=True)

# Handle form submission
if save_changes:
    if not new_name.strip(): #type: ignore
        st.error("Il campo nome non può essere vuoto")
    else:
        # Update user profile
        updates = {
            "name": new_name.strip(), #type: ignore
            "onboarding_completed": True  # Mark as completed if they're editing profile
        }

        user_id = get_user_id()
        success = get_auth_manager().update_user_profile(user_id, updates)

        if success:
            st.success("✅ Profilo aggiornato!")
            # Force refresh user data from database to get updated vision status
            get_auth_manager().refresh_user_data(user_id)
            st.rerun()
        else:
            st.error("X Errore nel salvataggio del profilo. Per favore riprova.")

if cancel_edit:
    st.info("Cambiamenti annullati.")

st.markdown("---")

# Danger zone
st.markdown("### ⚠️ Gestione Account")

with st.expander("🔐 Sicurezza & Dati", expanded=False):
    st.markdown("#### Opzioni di Sicurezza")

    col1, col2 = st.columns(2)

    with col1:
        if st.button("🔐 Cambia Password", use_container_width=True):
            st.info("Perfavore usa la funzione 'Forgot Password' sulla pagina di accesso.")

    with col2:
        if st.button("📊 Esporta i tuoi dati", use_container_width=True):
            st.info("Questa funzione sarà disponibile a breve!")

    st.markdown("#### Azioni sull'Account")

    col1, col2 = st.columns(2)

    with col1:
        if st.button("🗑️ Cancella Account", type="secondary", use_container_width=True):
            st.error("⚠️ La cancellazione Account è permanente e non può essere annullata!")
            st.markdown("Contatta <EMAIL> per eliminare il tuo account.")

    with col2:
        if st.button("🚪 Esci", use_container_width=True):
            get_auth_manager().logout_user()
            st.success("Logged out con successo!")
            st.rerun()

# Footer
st.markdown("---")
st.markdown(
    """
    <div style="text-align: center; color: #666; font-size: 0.8em;">
        <p>Hai bisogno di aiuto? <NAME_EMAIL></p>
    </div>
    """,
    unsafe_allow_html=True,
)

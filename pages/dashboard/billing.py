import streamlit as st
from utils.auth import get_auth_manager
from utils.stripe_utils import stripe_manager
import pandas as pd
from datetime import datetime, timedelta
import streamlit.components.v1 as components
from utils.config import config
from utils.credits import get_user_id
from components.help_chat import show_help_chat

# Require authentication (restoration handled in app.py)
get_auth_manager().require_auth()







credit_packages = [
    {"credits": 50, "price": 17.99, "bonus": 0, "popular": False},
    {"credits": 100, "price": 39.99, "bonus": 50, "popular": True},
    {"credits": 200, "price": 69.99, "bonus": 100, "popular": False}
]

# Debug: Show which user and token are being used for Stripe


# Add responsive CSS for billing page
st.markdown(
    """
    <style>
    /* Responsive design for billing page */
    @media (max-width: 768px) {
        .stColumns {
            flex-direction: column !important;
        }
        .stColumn {
            width: 100% !important;
            margin-bottom: 1rem;
        }
        .pricing-card {
            margin: 10px 5px !important;
        }
    }

    .pricing-card {
        transition: transform 0.2s ease-in-out;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .pricing-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .popular-badge {
        position: relative;
        top: -10px;
    }
    </style>
    """,
    unsafe_allow_html=True,
)

# Header
st.markdown(
    """
    <div style="text-align: center; padding: 2rem 0;">
        <h1 style='color: #222;'>Fatturazione & Crediti</h1>
        <p style='color: #666'>Un'offerta semplice e trasparente che si adatta alla tua necessità</p>
    </div>
    """,
    unsafe_allow_html=True,
)

# Navigation is now handled by the main app.py sidebar

# Current credits
st.markdown("### Saldo")

col1, col2, col3 = st.columns(3)
# Get real transaction data from database


with col1:
    user_id = get_user_id()
    transactions_data = get_auth_manager().get_user_transactions(user_id)
    transactions = []
    total_purchased = len(transactions_data)
    st.metric(
        label="Totale Acquisti",
        value=total_purchased,
        help="Numero di acquisti effettuati",
        border=True
    )

with col2:
    user_data = st.session_state.get("user_data", {})
    credits = user_data.get('credits', 0)
    st.metric(
        label="Credits Disponibili",
        value=credits,
        help="Credits disponibili per l'uso di servizi AI",
        border=True
    )

with col3:
    # Calculate credit status
    if credits > 50:
        status = "Perfetto!"
        delta_color = "normal"
    elif credits > 10:
        status = "Basso"
        delta_color = "inverse"
    else:
        status = "Critico!"
        delta_color = "inverse"

    st.metric(
        label="Credit Status",
        value=status,
        help="Your credit balance status",
        border=True
    )


st.markdown(
    """
    <div style="text-align: center; margin: 40px 0px;">
        <h2 style="text-align: center;  margin: 0; color: #333; font-weight: 700;">Scegli il tuo piano</h2>
        <p style="text-align: center;  color: #666; margin: 10px 0;">Un'offerta semplice e trasparente che si adatta alle tue esigenze</p>
    </div>
    """,
    unsafe_allow_html=True,
)

cols = st.columns(len(credit_packages))

for i, package in enumerate(credit_packages):
    with cols[i]:
        total_credits = package["credits"] + package["bonus"]
        price_per_credit = package["price"] / total_credits


        # Package card
        card_style = "border: 2px solid #ff6b6b;" if package["popular"] else "border: 1px solid #ddd;"

        st.markdown(
            f"""
            <div style="{card_style} padding: 20px; border-radius: 10px; text-align: center; margin: 10px 0;">
                {"<span style='background: #ff6b6b; color: white; padding: 5px 10px; border-radius: 15px;  line-height: 1.2;'><b>POPULAR</b></span><br><br>" if package["popular"] else "<span style=' color: white; padding: 5px 10px; border-radius: 15px; font-size: 0.8em;'></span><br><br>"}
                <h3>{package["credits"]} Crediti</h3>
                {f"<p style='color: #28a745;'>+{package['bonus']} Bonus Crediti!</p>" if package["bonus"] > 0 else "<p style='color: #28a745;'>&nbsp;</p>"}
                <h2>€{package["price"]}</h2>
                <p style='color: #666; font-size: 0.9em;'>€{price_per_credit:.3f} per credito</p>
            </div>
            """,
            unsafe_allow_html=True
        )

        # Payment button with on-demand session creation
        if st.button(f"Compra {total_credits} Crediti", key=f"buy_{package['credits']}", use_container_width=True, type="primary" if package["popular"] else "secondary"):
            # Only get refresh token when button is clicked
            refresh_token = get_auth_manager().get_session_token()

            if refresh_token:
                # Create Stripe session on-demand
                token_param = f"&token={refresh_token}"
                user_id = get_user_id()
                session = stripe_manager.create_credit_checkout_session(
                    user_id=user_id,
                    credit_package=package,
                    success_url=f"{config.app_url}?success=true{token_param}&session_id={{CHECKOUT_SESSION_ID}}",
                    cancel_url=f"{config.app_url}?canceled=true{token_param}",
                )

                if session and session.url:
                    # Redirect to Stripe checkout
                    st.markdown(f'<meta http-equiv="refresh" content="0; url={session.url}">', unsafe_allow_html=True)
                    st.success("ti stiamo reindirizzando...")
                else:
                    st.error("X Abbiamo falto nel creare la sessione di pagamento. Per favore riprova.")
            else:
                st.error("❌ Errore di autenticazione. Per favore riprova.")

st.markdown("---")


# Transaction history
st.markdown("### 📊 Storico delle transazioni")



for trans in transactions_data:
    transactions.append({
        "date": trans.get("created_at", "").split("T")[0] if trans.get("created_at") else "Sconosciuto",
        "type": trans.get("transaction_type", "").replace("_", " ").title(),
        "amount": f"€{trans.get('amount', 0):.2f}",
        "credits": f"+{trans.get('credits_purchased', 0)}",
        "status": trans.get("status", "").title()
    })

if transactions:
    # Create DataFrame for better display
    df = pd.DataFrame(transactions)

    # Display as a nice table
    st.dataframe(
        df,
        use_container_width=True,
        hide_index=True,
        column_config={
            "date": st.column_config.DateColumn("Date"),
            "type": "Transaction Type",
            "amount": "Amount",
            "credits": "Credits",
            "status": st.column_config.TextColumn("Status")
        }
    )

    # Download transactions
    csv = df.to_csv(index=False)
    st.download_button(
        label="📥 Scarica lo Storico delle Transazioni",
        data=csv,
        file_name=f"kdp_stratega_transactions_{datetime.now().strftime('%Y%m%d')}.csv",
        mime="text/csv"
    )
else:
    st.info("Non sono ancora state effettuate transazioni. Acquista crediti o aggiorna il tuo piano per visualizzare lo storico delle transazioni.")

st.markdown("---")

# Billing information
st.markdown("### 🏦 Informazioni di Fatturazione")

col1, col2 = st.columns(2)

with col1:
    st.markdown("#### Metodo di Pagamento")
    st.info("💳 Gestisci i tuoi metodi di pagamento tramite il portale del cliente di Stripe")

    if st.button("🔗 Apri il Portale del Cliente", use_container_width=True):
        st.info("🔄 Il portale del cliente verrà reindirizzato a Stripe per la gestione dei metodi di pagamento")
        # TODO: Implement customer portal when we have customer IDs
        # portal_session = stripe_manager.create_customer_portal_session(customer_id, return_url)

with col2:
    st.markdown("#### Indirizzo di Fatturazione")
    st.info("📍 Aggiorna il tuo indirizzo di fatturazione per la conformità fiscale")

    if st.button("✏️ Aggiorna Indirizzo", use_container_width=True):
        st.info("Il modulo di aggiornamento dell'indirizzo verrà implementato qui")

# Usage insights
st.markdown("---")
st.markdown("### 📈 Utilizzo dei Crediti")

col1, col2 = st.columns(2)

with col1:
    st.markdown("#### Sommario dell'Account")
    # # Get real credit usage data

    usage_history = get_auth_manager().get_user_credits_history(user_id, limit=30)
    st.markdown("#### Utilizzo dell'Account")
    user_id = get_user_id()
    usage_summary = get_auth_manager().get_user_credits_usage(user_id)
    # Calculate total credits used
    total_used = sum([item.get('total_credits_used', 0) for item in usage_summary])
    st.metric("Totale crediti usati", total_used)

    # Current balance
    user_data = st.session_state.get("user_data", {})
    current_credits = user_data.get('credits', 0)
    st.metric("Saldo corrente", current_credits)

    # Calculate recent usage (last 7 days)
    recent_usage = 0
    if usage_history:
        seven_days_ago = datetime.now() - timedelta(days=7)
        for record in usage_history:
            created_at = record.get('created_at', '')
            if created_at:
                try:
                    record_date = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                    if record_date >= seven_days_ago:
                        recent_usage += record.get('credits_used', 0)
                except:
                    pass

    st.metric("Crediti usati (7 giorni)", recent_usage)

with col2:

    if usage_summary:
        # Create DataFrame from real data
        usage_data = {
            "Feature": [],
            "Credits Used": []
        }

        for item in usage_summary:
            action_type = item.get('action_type', '').replace('_', ' ').title()
            credits_used = item.get('total_credits_used', 0)
            usage_data["Feature"].append(action_type)
            usage_data["Credits Used"].append(credits_used)

        if usage_data["Feature"]:
            usage_df = pd.DataFrame(usage_data)
            st.bar_chart(usage_df.set_index("Feature"))
        else:
            st.info("Non ci sono dati di utilizzo dei crediti disponibili.")
    else:
        st.info("Non ci sono dati di utilizzo dei crediti disponibili.")
# Detailed usage history
if usage_history:
    st.markdown("#### Recent Usage History")

    # Show last 10 records
    recent_records = usage_history[:10]
    history_data = []

    for record in recent_records:
        history_data.append({
            "Date": record.get("created_at", "").split("T")[0] if record.get("created_at") else "Unknown",
            "Action": record.get("action_type", "").replace("_", " ").title(),
            "Credits": record.get("credits_used", 0),
            "Description": record.get("description", "N/A")
        })

    if history_data:
        history_df = pd.DataFrame(history_data)
        st.dataframe(
            history_df,
            use_container_width=True,
            hide_index=True
        )

# Footer
st.markdown("---")
st.markdown(
    """
    <div style="text-align: center; color: #666; font-size: 0.8em;">
        <p>💡 Bisogno di aiuto sulla fatturazione? <a href="mailto:<EMAIL>"><EMAIL></a></p>
        <p>🔒 Tutti i pagamenti vengono elaborati in modo sicuro da Stripe</p>
    </div>
    """,
    unsafe_allow_html=True,
)

# Show help chat for dashboard pages
show_help_chat("dashboard")

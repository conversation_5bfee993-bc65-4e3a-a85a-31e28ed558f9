import streamlit as st
from utils.auth import get_auth_manager
import pandas as pd
from datetime import datetime, timedelta
from utils.credits import get_user_credits, get_user_id

# Require authentication (restoration handled in app.py)
get_auth_manager().require_auth()


st.markdown(
    f"""
    <h1 style='text-align: center; color: #222; font-size: 2.5em;'>📊 Dashboard</h1>
    <h3 style='text-align: center; color: #444;'>Welcome back, {st.session_state.get("user_data", {}).get('name', 'Author')}!</h3>
    """,
    unsafe_allow_html=True,
)

# Navigation is now handled by the main app.py sidebar

# Quick stats
col1, col2, col3, col4 = st.columns(4)

with col1:
    user_data = st.session_state.get("user_data", {})
    st.metric(
        label="💳 Crediti",
        value=user_data.get('credits', 0),
        help="AI analysis credits remaining",
        border=True

    )

with col2:
    user_data = st.session_state.get("user_data", {})
    st.metric(
        label="📊 Piano",
        value=user_data.get('subscription_status', 'Free').title(),
        help="Current subscription plan",
        border=True
    )

with col3:
    # Get project count
    user_id = get_user_id()
    projects = get_auth_manager().get_user_projects(user_id)
    project_count = len(projects) if projects else 0
    st.metric(
        label="📚 Progetti",
        value=project_count,
        help="Total book projects",
        border=True,
    )

with col4:
    # Check vision status
    user_id = get_user_id()
    has_vision = get_auth_manager().check_vision_exists(user_id)
    st.metric(
        label="🎯 Vision",
        value="✅ Complete" if has_vision else "➖ Optional",
        help="Author vision is optional - helps personalize your experience",
        border=True,
    )

st.markdown("---")

# Quick actions
st.markdown("### 🚀 Quick Actions")

col1, col2, col3 = st.columns(3)

with col1:
    if st.button("📚 New Book Project", type="primary", use_container_width=True):
        # Clear any existing project data and set new project flag
        st.session_state["creating_new_project"] = True
        st.session_state["from_projects_dashboard"] = True
        # Clear existing project info
        for key in ["current_project_id", "current_project_name", "current_book_topic"]:
            if key in st.session_state:
                del st.session_state[key]
        st.switch_page("pages/step1_home.py")

with col2:
    if st.button("🎯 Edit Vision", use_container_width=True):
        st.switch_page("pages/vision_builder.py")

with col3:
    if st.button("💰 Buy Credits", use_container_width=True):
        st.switch_page("pages/dashboard/billing.py")


st.html("<div style='height: 50px; display: flex;'>\n</div>")

if has_vision:
    user_id = get_user_id()
    vision_data = get_auth_manager().get_user_vision(user_id)
    if vision_data:
        st.markdown("### 🎯 Your Author Vision")

        st.markdown(f"{vision_data.get('vision', 'No vision available')}")
        st.caption(f"Created: {vision_data.get('created_at', 'Unknown')}")
else:
    st.info("💡 Create an Author Vision to unlock personalized features (optional)")
    if st.button("🎯 Create Vision (Optional)"):
        st.switch_page("pages/vision_builder.py")


st.html("<div style='height: 50px; display: flex;'>\n</div>")

# Recent activity
st.markdown("---")
st.markdown("### 📈 Recent Activity")

if projects and len(projects) > 0:
    st.markdown("#### 📚 Recent Projects")

    # Show last 3 projects
    recent_projects = projects[:3]

    for project in recent_projects:
        with st.expander(f"📖 {project.get('project_name', 'Untitled Project')}", expanded=False):
            col1, col2 = st.columns([3, 1])

            with col1:
                st.markdown(f"**Status:** {project.get('status', 'draft').title()}")
                st.markdown(f"**Progress:** {project.get('progress_percentage', 0)}%")
                st.markdown(f"**Last Updated:** {project.get('updated_at', 'Unknown')}")

                if project.get('final_result'):
                    st.success("✅ Project completed!")

            with col2:
                if st.button(f"📂 Open", key=f"open_{project['id']}"):
                    st.info("Project loading feature coming soon!")
else:
    st.info("No projects yet. Create your first book project to get started!")

# Vision status


st.html("<div style='height: 50px; display: flex;'>\n</div>")

col1, col2, col3 = st.columns(3)

with col1:
    if st.button("📊 View All Projects", use_container_width=True):
        st.switch_page("pages/dashboard/projects.py")

with col2:
    if st.button("💰 Billing & Credits", use_container_width=True):
        st.switch_page("pages/dashboard/billing.py")

with col3:
    if st.button("🚪 Logout", use_container_width=True, type="secondary"):
        get_auth_manager().logout_user()
        st.success("Logged out successfully!")
        st.rerun()

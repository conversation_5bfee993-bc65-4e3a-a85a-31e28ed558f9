{"search_query": "python programming", "search_date": "2025-06-14T18:49:28.537576", "results_by_marketplace": {"US": {"domain": "amazon.com", "kindle_category_id": 133140011, "books": [{"asin": "B01GSODGZC", "title": "PYTHON: PROGRAMMING: A BEGINNER’S GUIDE TO LEARN PYTHON IN 7 DAYS", "sales_rank": 999999, "review_count": 0, "rating": null}, {"asin": "B07ZT1WR22", "title": "Programming for Computations - Python: A Gentle Introduction to Numerical Simula", "sales_rank": 999999, "review_count": 0, "rating": null}, {"asin": "B08C7GNS3N", "title": "Introduction to Scientific Programming with Python (<PERSON><PERSON>la <PERSON>riefs on Com", "sales_rank": 999999, "review_count": 0, "rating": null}, {"asin": "B0C3FQJ45T", "title": "Python For Beginners: A Practical and Step-by-Step Guide to Programming with Pyt", "sales_rank": 999999, "review_count": 0, "rating": null}, {"asin": "B0DHYK2DPQ", "title": "Automate with Python: Unlock the Power of Python: Automate Everyday Tasks with E", "sales_rank": 999999, "review_count": 0, "rating": null}]}, "GB": {"domain": "amazon.co.uk", "kindle_category_id": 341677031, "books": [{"asin": "B078YGVNSF", "title": "Programming for Computations - Python: A Gentle Introduction to Numerical Simula", "sales_rank": 999999, "review_count": 0, "rating": null}, {"asin": "B07ZT1WR22", "title": "Programming for Computations - Python: A Gentle Introduction to Numerical Simula", "sales_rank": 999999, "review_count": 0, "rating": null}, {"asin": "B08C7GNS3N", "title": "Introduction to Scientific Programming with Python (<PERSON><PERSON>la <PERSON>riefs on Com", "sales_rank": 999999, "review_count": 0, "rating": null}, {"asin": "B0C3FQJ45T", "title": "Python For Beginners: A Practical and Step-by-Step Guide to Programming with Pyt", "sales_rank": 999999, "review_count": 0, "rating": null}, {"asin": "B0CXYVGSF7", "title": "Python Programming for Beginners Made Easy: Learn the Essentials in 7 Days and F", "sales_rank": 999999, "review_count": 0, "rating": null}]}, "DE": {"domain": "amazon.de", "kindle_category_id": 530484031, "books": [{"asin": "B07MDNJH6Q", "title": "Python Programming for Beginners: Learn Python Machine Learning Language From Sc", "sales_rank": 999999, "review_count": 0, "rating": null}, {"asin": "B08HJNNPPC", "title": "Python&Xml.pdf (Programming books Book 1111) (English Edition)", "sales_rank": 999999, "review_count": 0, "rating": null}, {"asin": "B09N29C8WT", "title": "Python Programming with Google Colab : A beginner's Hand Book (English Edition)", "sales_rank": 999999, "review_count": 0, "rating": null}, {"asin": "B0BBPK5JYV", "title": "Python Cheat sheet: A cheat sheet that contain over 95% of python 3 commands wit", "sales_rank": 999999, "review_count": 0, "rating": null}, {"asin": "B0D9XRBM1C", "title": "बिल्कुल Beginners के लिए Python Programming Guide: हैंड्स-ऑन, वास्तविक दुनिया के", "sales_rank": 999999, "review_count": 0, "rating": null}]}, "ES": {"domain": "amazon.es", "kindle_category_id": 827231031, "books": [{"asin": "B078YGVNSF", "title": "Programming for Computations - Python: A Gentle Introduction to Numerical Simula", "sales_rank": 999999, "review_count": 0, "rating": null}, {"asin": "B07ZT1WR22", "title": "Programming for Computations - Python: A Gentle Introduction to Numerical Simula", "sales_rank": 999999, "review_count": 0, "rating": null}, {"asin": "B08C7GNS3N", "title": "Introduction to Scientific Programming with Python (<PERSON><PERSON>la <PERSON>riefs on Com", "sales_rank": 999999, "review_count": 0, "rating": null}, {"asin": "B08JH7L94Q", "title": "Python Crash Course: The Step by Step Python for Everybody Book for Learning Pyt", "sales_rank": 999999, "review_count": 0, "rating": null}, {"asin": "B0BGYJ7G6T", "title": "Python Programming Exercises, Gently Explained (English Edition)", "sales_rank": 999999, "review_count": 0, "rating": null}]}, "IT": {"domain": "amazon.it", "kindle_category_id": 827182031, "books": [{"asin": "B07CGFJHH7", "title": "Python Programming: Your Advanced Guide To Learn Python in 7 Days: ( python guid", "sales_rank": 999999, "review_count": 0, "rating": null}, {"asin": "B083F6SLJZ", "title": "python for data science: the ultimate guide for beginners.machine learning tools", "sales_rank": 999999, "review_count": 0, "rating": null}, {"asin": "B09ZDR37Z3", "title": "Network Programming in Python: The Basic: A Detailed Guide to Python 3 Network P", "sales_rank": 999999, "review_count": 0, "rating": null}, {"asin": "B0CP7Y3T4T", "title": "Introduction to Python Programming - Data Structures & Algorithms (English Editi", "sales_rank": 999999, "review_count": 0, "rating": null}, {"asin": "B0F71QF2KV", "title": "A BEGINNER’S GUIDE TO Learn Python Programming (English Edition)", "sales_rank": 999999, "review_count": 0, "rating": null}]}}}
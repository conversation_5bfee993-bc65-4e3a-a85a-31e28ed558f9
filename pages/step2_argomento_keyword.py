from streamlit.deprecation_util import TObj
from utils.auth import get_auth_manager
from components.help_chat import show_help_chat

# Require authentication
get_auth_manager().require_auth()
from urllib import response
import streamlit as st
import requests  # Added
import re  # Added
import json
import time
import keepa
from typing import List, Dict, Any

from utils.fonti_web import cerca_fonti
from utils.config import config
from utils.auth import get_auth_manager
from agno.agent import Agent
from utils.credits import get_user_credits, get_user_id

# Require authentication
get_auth_manager().require_auth()


# Removed: from agno.models.perplexity import Perplexity
from agno.tools.serpapi import SerpApiTools
from pydantic import BaseModel
from typing import List, Dict, Any

# Import Agno for SerpAPI fallback
from agno.agent import Agent
from agno.tools.serpapi import SerpApiTools


class AmazonBook(BaseModel):
    title: str
    author: str
    url: str
    reviews_count: int = 0
    marketplace: str


class AmazonBooksResponse(BaseModel):
    books: List[AmazonBook]


def get_amazon_books_keepa(query: str, marketplace: str) -> List[Dict[str, Any]]:
    """
    Search books on Amazon using Keepa Python library - simplified to get ASINs only
    """
    keepa_key = config.KEEPA_KEY
    if not keepa_key:
        st.warning("⚠️ Chiave Keepa API mancante. Ricerca non disponibile.")
        return []

    # Map marketplace to Keepa domain codes (string format for Python library)
    marketplace_mapping = {
        'IT': {'domain_code': 'IT', 'kindle_category': 827182031},
        'US': {'domain_code': 'US', 'kindle_category': 133140011},
        'UK': {'domain_code': 'GB', 'kindle_category': 341677031},  # Note: UK -> GB
        'DE': {'domain_code': 'DE', 'kindle_category': 530484031},
        'FR': {'domain_code': 'FR', 'kindle_category': 672108031},
        'ES': {'domain_code': 'ES', 'kindle_category': 827231031}
    }

    if marketplace not in marketplace_mapping:
        st.warning(f"⚠️ Marketplace {marketplace} non supportato")
        return []

    market_info = marketplace_mapping[marketplace]

    # Domain URLs for reconstructing Amazon links
    domain_urls = {
        'IT': 'amazon.it',
        'US': 'amazon.com',
        'UK': 'amazon.co.uk',
        'DE': 'amazon.de',
        'FR': 'amazon.fr',
        'ES': 'amazon.es'
    }

    try:
        # Initialize Keepa API with timeout handling
        with st.spinner("🔍 Connessione a Keepa API..."):
            try:
                api = keepa.Keepa(keepa_key, timeout=15)
            except Exception as e:
                error_str = str(e).lower()
                if "timeout" in error_str:
                    st.warning("⚠️ Timeout connessione Keepa API. Riprova più tardi.")
                    return []
                elif "nodename nor servname" in error_str or "errno 8" in error_str:
                    st.warning("⚠️ Errore di connessione di rete. Verifica la connessione internet e riprova.")
                    return []
                elif "name resolution" in error_str or "dns" in error_str:
                    st.warning("⚠️ Errore DNS. Verifica la connessione internet o cambia server DNS.")
                    return []
                else:
                    st.warning(f"⚠️ Errore connessione Keepa: {str(e)}")
                    return []

        # Check API status
        try:
            tokens_left = api.tokens_left
            if tokens_left <= 0:
                st.info("ℹ️ Quota API Keepa esaurita. Riprova più tardi.")
                return []
            elif tokens_left < 20:
                st.info(f"ℹ️ Token rimanenti: {tokens_left} (quota bassa)")
        except:
            st.info("ℹ️ Impossibile verificare quota API")

        # Search for books using product_finder (ASINs only, sorted by sales rank)
        with st.spinner(f"🔍 Ricerca libri su {marketplace}..."):
            try:
                product_params = {
                    'title': query,
                    'categories_include': [market_info['kindle_category']],
                    'sort': ["current_SALES", "asc"],  # Sort by sales rank (best sellers first)
                }

                # Get ASINs using product_finder - already sorted by sales rank
                asins = api.product_finder(
                    product_params,
                    domain=market_info['domain_code']
                )

            except Exception as e:
                error_str = str(e)
                if "REQUEST_REJECTED" in error_str:
                    st.warning("⚠️ Richiesta rifiutata da Keepa. Quota API esaurita o limite raggiunto.")
                    return []
                elif "nodename nor servname" in error_str.lower() or "errno 8" in error_str.lower():
                    st.warning("⚠️ Errore di connessione di rete durante la ricerca. Verifica la connessione internet.")
                    return []
                elif "timeout" in error_str.lower():
                    st.warning("⚠️ Timeout durante la ricerca. Riprova più tardi.")
                    return []
                else:
                    st.warning(f"⚠️ Errore ricerca Keepa: {str(e)}")
                    return []

        if not asins:
            st.info(f"📚 Nessun libro trovato per '{query}' su {marketplace}")
            return []

        st.success(f"✅ Trovati {len(asins)} libri su {marketplace}")

        # Get product details for the ASINs (limit to 10 to save tokens)
        books_data = []
        try:
            with st.spinner("📖 Recupero dettagli libri..."):
                # Query for product details with ratings
                products = api.query(asins[:10], domain=market_info['domain_code'], stats=30, history=False, rating=True)

                for product in products:
                    try:
                        asin = product.get('asin', 'N/A')
                        title = product.get('title', 'Titolo non disponibile')

                        # Get sales rank, review count, and rating
                        sales_rank = None
                        review_count = 0
                        rating = None

                        # Extract sales rank from different possible locations
                        if 'salesRanks' in product and product['salesRanks']:
                            # Get the main sales rank (usually the first one)
                            for rank_data in product['salesRanks'].values():
                                if isinstance(rank_data, list) and rank_data:
                                    sales_rank = rank_data[-1]  # Latest sales rank
                                    break

                        # Extract from stats if available
                        if sales_rank is None and 'stats' in product and product['stats']:
                            stats = product['stats']
                            if isinstance(stats, dict) and 'current' in stats:
                                current = stats['current']
                                if isinstance(current, list) and len(current) > 3:
                                    # Index 3 is typically SALES rank in the stats array
                                    sales_rank = current[3] if current[3] and current[3] > 0 else None

                        # Extract review count and rating
                        if 'csv' in product and isinstance(product['csv'], list):
                            # Index 16 is typically COUNT_REVIEWS
                            if len(product['csv']) > 16 and product['csv'][16]:
                                review_data = product['csv'][16]
                                if isinstance(review_data, list) and review_data:
                                    review_count = review_data[-1] if review_data[-1] else 0

                            # Index 17 is typically RATING
                            if len(product['csv']) > 17 and product['csv'][17]:
                                rating_data = product['csv'][17]
                                if isinstance(rating_data, list) and rating_data:
                                    rating_raw = rating_data[-1]
                                    if rating_raw and rating_raw > 0:
                                        rating = rating_raw / 10.0

                        # Get author if available
                        author = product.get('author', '')
                        if isinstance(author, list):
                            author = ', '.join(author)

                        # Reconstruct Amazon URL
                        amazon_url = f"https://{domain_urls[marketplace]}/dp/{asin}"

                        # Ensure we have valid values for display
                        if sales_rank is None or sales_rank <= 0:
                            sales_rank = None  # Keep as None for "N/A" display

                        book_data = {
                            "title": title[:100] if title else "Titolo non disponibile",
                            "author": author if author else "Autore non disponibile",
                            "url": amazon_url,
                            "reviews_count": review_count if review_count else 0,
                            "marketplace": marketplace,
                            "asin": asin,
                            "sales_rank": sales_rank,
                            "rating": rating if rating and rating > 0 else None,
                            "source": "Keepa API"
                        }

                        books_data.append(book_data)

                    except Exception as e:
                        st.warning(f"⚠️ Errore elaborazione libro: {str(e)}")
                        continue

                # Sort by sales rank (lower is better), with fallback for None values
                # Books with sales rank come first, then by review count
                books_data.sort(key=lambda x: (
                    x['sales_rank'] if x['sales_rank'] is not None else 999999,
                    -x.get('reviews_count', 0)
                ))

                # Ensure we return top 10 sorted by sales rank
                return books_data[:10]

        except Exception as e:
            st.warning(f"⚠️ Errore recupero dettagli: {str(e)}. Ritorno solo ASINs con ranking.")
            # Fallback to simple ASIN list - preserve sales rank order
            books_data = []
            for i, asin in enumerate(asins[:10], 1):
                amazon_url = f"https://{domain_urls[marketplace]}/dp/{asin}"
                book_data = {
                    "title": f"Prodotto Kindle - ASIN: {asin}",
                    "author": "Autore non disponibile",
                    "url": amazon_url,
                    "reviews_count": 0,
                    "marketplace": marketplace,
                    "asin": asin,
                    "sales_rank": i,  # Position in results indicates sales rank order
                    "rating": None,
                    "source": "Keepa API (solo ranking)"
                }
                books_data.append(book_data)

            st.info(f"ℹ️ I libri sono ordinati per ranking vendite (1 = più venduto)")
            return books_data

    except Exception as e:
        error_str = str(e).lower()
        if "nodename nor servname" in error_str or "errno 8" in error_str:
            st.error("❌ Errore di connessione di rete. Verifica la tua connessione internet e riprova.")
        elif "timeout" in error_str:
            st.error("❌ Timeout di connessione. Verifica la connessione e riprova più tardi.")
        elif "dns" in error_str or "name resolution" in error_str:
            st.error("❌ Errore DNS. Prova a cambiare server DNS o verifica la connessione.")
        else:
            st.error(f"❌ Errore generale Keepa: {str(e)}")
        return []


def get_amazon_books_serpapi(query: str, marketplace: str) -> List[Dict[str, Any]]:
    """
    Search books on Amazon using Agno SerpAPI toolkit with structured output
    """
    serpapi_key = config.SERPAPI_API_KEY
    if not serpapi_key:
        st.warning("⚠️ Chiave SerpAPI mancante. Ricerca con SerpAPI non disponibile.")
        return []

    # Map marketplace to Amazon domain
    amazon_domains = {
        "IT": "amazon.it",
        "US": "amazon.com",
        "UK": "amazon.co.uk",
        "DE": "amazon.de",
        "FR": "amazon.fr",
        "ES": "amazon.es",
    }
    domain = amazon_domains.get(marketplace, "amazon.com")

    try:
        # Create Agno agent with SerpAPI tools and structured output
        agent = Agent(
            tools=[SerpApiTools(api_key=serpapi_key)],
            instructions=f"Sei un assistente specializzato nella ricerca di libri con elevato numero di recensioni su Amazon. Cerca libri e ebook con elevato numero di recensioni su {domain} e restituisci SEMPRE dati strutturati validi.",
            response_model=AmazonBooksResponse,
        )

        # Execute search with structured output
        response = agent.run(
            f"'site:{domain} {query}'"
            f"Trova almeno 5-10 libri o ebook e per ognuno estrai: "
            f"- Titolo del libro "
            f"- Autore"
            f"- URL completo Amazon"
            f"- Numero di recensioni se disponibile "
            f"- ordina i libri per numero di recensioni"
            f"IMPORTANTE: Gli URL devono essere reali e funzionanti, non placeholder e devono assolutamente essere dei libri o ebook - non prodotti amazon!"
        )

        # According to Agno docs, response_model returns the Pydantic model directly
        books_data = []

        for book in response.content.books:  # type: ignore
            books_data.append(
                {
                    "title": book.title,
                    "author": book.author,
                    "url": book.url,
                    "reviews_count": book.reviews_count,
                    "marketplace": book.marketplace,
                    "asin": "",  # SerpAPI doesn't provide ASIN
                    "sales_rank": 0,  # SerpAPI doesn't provide sales rank
                    "rating": None,  # SerpAPI doesn't provide rating
                    "source": "SerpAPI (Fallback)",
                }
            )
        return books_data

    except Exception as e:
        st.error(f"❌ Errore nella ricerca SerpAPI: {str(e)}")
        return []


def get_amazon_books(query: str, marketplace: str) -> List[Dict[str, Any]]:
    """
    Search books on Amazon - first try Keepa, then fallback to SerpAPI if no results
    """
    # First try with Keepa
    keepa_results = get_amazon_books_keepa(query, marketplace)

    # If Keepa returns books, use them
    if keepa_results:
        return keepa_results

    # If Keepa returns 0 books, try SerpAPI as fallback
    st.info("📚 Nessun risultato con Keepa. Provo con ricerca alternativa...")
    serpapi_results = get_amazon_books_serpapi(query, marketplace)

    return serpapi_results


def show_progress():
    """Display progress bar"""
    st.progress(2/12, text=f"Step 2 of 12 - {int(min(2 / 11, 1.0) * 100)}% Complete")


def get_current_book_topic():
    """Get current book topic from session state"""
    # Prefer argomento_keyword if available
    return st.session_state.get("argomento_keyword") or st.session_state.get("current_book_topic", "")



# Progress bar
show_progress()

# Main content
st.markdown("""
<h1 style='text-align: center; color: #222;'>📚 Scegli l'Argomento e Trova i Competitor</h1>
<p style='text-align: center; color: #666;'>Definisci l'argomento del tuo libro e analizza la concorrenza su Amazon</p>
""", unsafe_allow_html=True)

# Get current project info
current_topic = get_current_book_topic()

# Topic input
st.markdown("### 🎯 Argomento del Libro")
argomento_keyword = st.text_area(
    "Descrivi brevemente l'argomento del tuo libro:",
    value=current_topic,
    height=100,
    placeholder="Es: Guida pratica per la meditazione mindfulness per principianti",
    help="Scrivi una descrizione chiara e specifica del tuo argomento. Questo verrà usato per cercare libri simili su Amazon."
)

# Store the topic in session state
if argomento_keyword:
    st.session_state["argomento_keyword"] = argomento_keyword

# Market selection info
marketplace = st.session_state.get("marketplace", "IT")
st.info(f"📍 Ricerca configurata per: **{marketplace}** (Amazon.{marketplace.lower()})")

# Analysis and search section
if argomento_keyword:
    st.markdown("### 🔍 Analisi e Ricerca")

    # Check if regeneration was triggered
    if st.session_state.get("trigger_regeneration"):
        st.session_state.pop("trigger_regeneration")
        trigger_analysis = True
    else:
        trigger_analysis = st.button("🔍 Genera Analisi & Ricerca (20 crediti)", type="primary", use_container_width=True)

    if trigger_analysis:
        # Check credits before starting - always 20 credits now
        if get_user_credits() < 20:
            st.error("❌ Crediti insufficienti! Questa operazione richiede 20 crediti.")
            st.info("💳 Puoi acquistare più crediti dalla dashboard.")
            st.stop()

        # OpenAI Analysis
        openai_key = config.OPENAI_API_KEY
        if not openai_key:
            st.error("Chiave OpenAI mancante. Inseriscila nella Home.")
            st.stop()

        try:
            # Get marketplace info
            marketplace_languages = {
                "IT": "Italiano",
                "US": "Americano",
                "UK": "Inglese",
                "DE": "Tedesco",
                "FR": "Francese",
                "ES": "Spagnolo",
            }
            mercato = marketplace_languages.get(marketplace, "")

            # Usa Perplexity per l'analisi completa
            perplexity_api_key = config.PERPLEXITY_API_KEY
            with st.spinner("Ricerca della keyword su tutto il web in corso..."):
                if perplexity_api_key:
                    api_response = None
                    try:
                        headers = {
                            "Authorization": f"Bearer {perplexity_api_key}",
                            "Content-Type": "application/json",
                            "Accept": "application/json",
                        }

                        system_prompt_content = (
                            "Sei un editor esperto in self publishing Amazon KDP. "
                            "Genera un report di analisi di mercato dettagliato basato su ricerche web. "
                            "Il report DEVE essere in formato Markdown. "
                            "Additional Rules:"
                            "- Always print the full URL instead of just the Citation Number adding a space to separate the link from the text. Do not include []"
                            "- Include a List of References with the full URL in your answer"
                            "- Always cite the sources of your web search"
                        )

                        user_prompt_content = (
                            f"Analizza l'opportunità editoriale dell'argomento '{argomento_keyword}' nel mercato {mercato} "
                            f"Sezione: Fornisci un'analisi dettagliata basata sui dati di ricerca più recenti, cercando informazioni aggiornate su trend, competitor, target di mercato, nicchie emergenti e potenziali titoli "
                            f"Sezione: Determinare l'argomento preciso del libro. "
                            f"Sezione: Capire cosa cercano le persone su Google, Amazon, Reddit, Quora e forum fornendo link ed evidenze sotto forma di citazioni numerate (es. [1], [2]). "
                            f"Sezione pain point, linguaggio, insight. "
                            f"Sezione con consigli per adattarsi al marketplace {marketplace} scelto (lingua, cultura, richieste). "
                            f"Mostrare link reali cliccabili (blog, community, thread) attraverso le citazioni. "
                        )

                        messages = [
                            {"role": "system", "content": system_prompt_content},
                            {"role": "user", "content": user_prompt_content},
                        ]
                        payload = {
                            "model": "sonar-pro",
                            "messages": messages,
                        }
                        api_url = "https://api.perplexity.ai/chat/completions"
                        api_response = requests.post(
                            api_url,
                            json=payload,
                            headers=headers,
                        )
                        api_response.raise_for_status()
                        response_data = api_response.json()

                        if (
                            response_data
                            and response_data.get("choices")
                            and response_data["choices"][0].get("message")
                            and response_data["choices"][0]["message"].get("content")
                        ):
                            body = response_data["choices"][0]["message"]["content"]

                            # Process references
                            references_data = response_data.get("references", [])
                            if not references_data and response_data["choices"][0]["message"].get("context"):
                                references_data = response_data["choices"][0]["message"]["context"].get("references", [])

                            sources = {}
                            if references_data:
                                for ref in references_data:
                                    if "id" in ref and "url" in ref:
                                        sources[str(ref["id"])] = ref["url"]
                                    elif "number" in ref and "url" in ref:
                                        sources[str(ref["number"])] = ref["url"]

                            def replace_refs(match):
                                idx = match.group(1)
                                url = sources.get(idx, "#")
                                return f"[{idx}]({url})"

                            body_md = re.sub(r"\[(\d+)\]", replace_refs, body)

                            st.session_state["analisi_argomento_generata"] = body_md
                            st.markdown(body_md, unsafe_allow_html=False)

                            # Deduct credits for successful analysis
                            user_id = get_user_id()
                            get_auth_manager().deduct_credits(user_id, "content_generation", 10, description="Perplexity analysis")

                        else:
                            st.session_state["analisi_argomento_generata"] = (
                                "Analisi non disponibile o formato risposta inatteso. 🙈"
                            )
                            st.markdown(
                                "Analisi non disponibile o formato risposta inatteso."
                            )

                    except requests.exceptions.HTTPError as http_err:
                        error_message = (
                            f"Errore HTTP nell'analisi con Perplexity: {http_err}"
                        )
                        if api_response is not None and hasattr(api_response, "text"):
                            error_message += f" - {api_response.text}"
                        st.error(error_message)
                        st.session_state["analisi_argomento_generata"] = None
                    except Exception as e_perplexity:
                        st.error(
                            f"Errore nell'analisi con Perplexity: {str(e_perplexity)}"
                        )
                        st.session_state["analisi_argomento_generata"] = None
                else:
                    st.warning(
                        "Chiave API Perplexity mancante. Inseriscila nella Home o controlla la configurazione."
                    )

            # Amazon Books Search with Keepa
            with st.spinner("Ricerca libri su Amazon con Keepa API in corso..."):
                amazon_books = get_amazon_books(argomento_keyword, marketplace)
                st.session_state["amazon_books_data"] = amazon_books

                # Deduct credits for Amazon search
                user_id = get_user_id()
                get_auth_manager().deduct_credits(user_id, "content_generation", 10, description="Amazon books search")

                if amazon_books:
                    st.success(f"✅ Trovati {len(amazon_books)} libri simili!")
                else:
                    st.warning("⚠️ Nessun libro trovato per questa ricerca.")

            # Save step 2 data
            current_project_id = st.session_state.get("current_project_id")
            if current_project_id:
                step_data = {
                    "argomento_keyword": argomento_keyword.strip(),
                    "analisi_argomento_generata": st.session_state.get("analisi_argomento_generata", ""),
                    "amazon_books_data": st.session_state.get("amazon_books_data", [])
                }
                user_id = get_user_id()
                get_auth_manager().save_step_data(user_id, current_project_id, 2, step_data)

        except Exception as e:
            st.error(f"Errore generale nell'analisi: {e}")
            st.session_state["analisi_argomento_generata"] = None
            st.stop()

    # Show regenerate buttons if analysis exists
    if st.session_state.get("analisi_argomento_generata"):
        if st.button("🔄 Rigenera Analisi Completa (20 crediti)", use_container_width=True):
            if get_user_credits() < 20:
                st.error("❌ Crediti insufficienti! Servono 20 crediti per rigenerare l'analisi completa.")
                st.stop()

            # Clear previous results
            st.session_state.pop("analisi_argomento_generata", None)
            st.session_state.pop("amazon_books_data", None)

            # Trigger full regeneration by rerunning with a flag
            st.session_state["trigger_regeneration"] = True
            st.rerun()

# Display results if available
if "amazon_books_data" in st.session_state and st.session_state["amazon_books_data"]:
    amazon_books = st.session_state["amazon_books_data"]

    # Check if amazon_books contains valid dictionary data
    if isinstance(amazon_books, list) and amazon_books and isinstance(amazon_books[0], dict):
        st.markdown("### 📊 Libri Trovati")

        # Display summary stats
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("📚 Libri Trovati", len(amazon_books))
        with col2:
            avg_reviews = sum(book.get('reviews_count', 0) for book in amazon_books) / len(amazon_books) if amazon_books else 0
            st.metric("⭐ Media Recensioni", f"{avg_reviews:.0f}")
        with col3:
            books_with_rank = [book for book in amazon_books if book.get('sales_rank') is not None]
            if books_with_rank:
                best_rank = min(book['sales_rank'] for book in books_with_rank)
                st.metric("🏆 Miglior Ranking", f"#{best_rank:,}")
            else:
                st.metric("🏆 Miglior Ranking", "N/A")
        with col4:
            rated_books = [book for book in amazon_books if book.get('rating') is not None]
            if rated_books:
                avg_rating = sum(book['rating'] for book in rated_books) / len(rated_books)
                st.metric("⭐ Rating Medio", f"{avg_rating:.1f}/5")
            else:
                st.metric("⭐ Rating Medio", "N/A")

        # Display books in a nice format
        for i, book in enumerate(amazon_books, 1):
            with st.container():
                col1, col2 = st.columns([3, 1])

                with col1:
                    # Book title and details
                    st.markdown(f"**{i}. {book.get('title', 'Titolo non disponibile')}**")

                    # Book metrics
                    metrics = []
                    if book.get('sales_rank') is not None:
                        metrics.append(f"🏆 Ranking: #{book['sales_rank']:,}")
                    else:
                        metrics.append("🏆 Ranking: N/A")

                    review_count = book.get('reviews_count', 0)
                    if review_count > 0:
                        metrics.append(f"📝 {review_count} recensioni")
                    else:
                        metrics.append("📝 Recensioni: N/A")

                    if book.get('rating') is not None:
                        metrics.append(f"⭐ {book['rating']:.1f}/5")
                    else:
                        metrics.append("⭐ Rating: N/A")

                    if metrics:
                        st.caption(" • ".join(metrics))

                    # ASIN for reference
                    if book.get('asin'):
                        st.caption(f"ASIN: {book['asin']}")

                with col2:
                    if book.get('url'):
                        st.link_button("📖 Vedi su Amazon", book['url'], use_container_width=True)

                st.divider()

        # Action buttons
        st.markdown("### 🎯 Prossimo Step")
        col1, col2 = st.columns(2)

        with col1:
            if st.button("🔍 Analizza Recensioni", type="primary", use_container_width=True):
                st.switch_page("pages/step3_recensioni.py")

        with col2:
            if st.button("📝 Modifica Ricerca", use_container_width=True):
                if "amazon_books" in st.session_state:
                    del st.session_state["amazon_books"]
                st.rerun()

    else:
        # Clear invalid session state data
        st.warning("⚠️ Dati non validi trovati. Riprova la ricerca.")
        if "amazon_books" in st.session_state:
            del st.session_state["amazon_books"]
        st.rerun()

else:
    # Show help if no results
    st.markdown("### 💡 Come Procedere")
    st.info("""
    1. **Inserisci l'argomento** del tuo libro nell'area di testo sopra
    2. **Clicca 'Cerca libri simili'** per trovare competitor su Amazon
    3. **Analizza i risultati** per capire il mercato
    4. **Procedi al Step 3** per analizzare le recensioni
    """)

# Save progress
if argomento_keyword:
    try:
        # Update session state
        st.session_state["argomento_keyword"] = argomento_keyword

        # Save to database if we have a project
        current_project_id = st.session_state.get("current_project_id")
        if current_project_id:
            step_data = {
                "argomento_keyword": argomento_keyword,
                "amazon_books_data": st.session_state.get("amazon_books_data", []),
                "completed": True
            }

            user_id = get_user_id()
            result = get_auth_manager().save_step_data(user_id, current_project_id, 2, step_data)
            if result.get("success"):
                st.session_state["last_step_completed"] = 2

    except Exception as e:
        # Fail silently for save errors
        pass

# Show help chat for step2
show_help_chat("step2_argomento_keyword")

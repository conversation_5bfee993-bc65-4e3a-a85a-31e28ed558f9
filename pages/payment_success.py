import streamlit as st
from utils.auth import get_auth_manager
from utils.stripe_utils import stripe_manager

# Try to restore authentication from Supabase before requiring auth
if not st.session_state.get("authenticated", False):
    get_auth_manager().restore_session_from_supabase()

# Require authentication (now with Supabase Auth restoration)
get_auth_manager().require_auth()

# Function to get user data and validate
def get_validated_user():
    """Get user data and ensure user_id is valid"""
    user_data = st.session_state.get("user_data", {})
    user_id = user_data.get("id")

    if not user_id:
        st.error("User ID not found. Please login again.")
        st.rerun()
        st.stop()

    return user_data, user_id

# Get user info when needed
user_data, user_id = get_validated_user()

# Header
st.markdown(
    """
    <h1 style='text-align: center; color: #28a745; font-size: 2.5em;'>🎉 Payment Successful!</h1>
    <h3 style='text-align: center; color: #444;'>Thank you for your purchase</h3>
    """,
    unsafe_allow_html=True,
)

# Get session ID from query parameters
query_params = st.query_params
session_id = query_params.get("session_id")

if session_id:
    # Handle the successful payment
    success = stripe_manager.handle_successful_payment(session_id)

    if success:
        st.success("✅ Your payment has been processed successfully!")


        # Refresh user data
        updated_user = get_auth_manager().get_user_profile(user_id)
        if updated_user:
            st.session_state["user_data"].update(updated_user)

        # Display updated credit balance
        st.markdown("### 💳 Updated Account Information")

        col1, col2 = st.columns(2)

        with col1:
            new_credits = updated_user.get("credits", 0) if updated_user else user_data.get("credits", 0)
            st.metric(
                label="Current Credits",
                value=new_credits,
                help="Your updated credit balance"
            )

        with col2:
            plan = updated_user.get("subscription_status", "free") if updated_user else user_data.get("subscription_status", "free")
            st.metric(
                label="Current Plan",
                value=plan.title(),
                help="Your active subscription plan"
            )

        st.markdown("---")

        # Next steps
        st.markdown("### 🚀 What's Next?")

        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("📊 View Dashboard", type="primary", use_container_width=True):
                st.rerun()

        with col2:
            if st.button("📚 Start New Project", use_container_width=True):
                st.rerun()

        with col3:
            if st.button("💰 View Billing", use_container_width=True):
                st.rerun()

        # Receipt information
        st.markdown("### 📧 Receipt & Support")
        st.info("📧 A receipt has been sent to your email address. If you have any questions, please contact our support team.")

    else:
        st.error("❌ There was an issue processing your payment. Please contact support.")

        if st.button("🔄 Try Again", type="primary"):
            st.rerun()
else:
    st.error("❌ No payment session found. Please try again.")

    if st.button("🔄 Go to Billing", type="primary"):
        st.rerun()

# Footer
st.markdown("---")
st.markdown(
    """
    <div style="text-align: center; color: #666; font-size: 0.9em;">
        <p>🔒 Your payment was securely processed by Stripe</p>
        <p>📞 Need help? Contact <a href="mailto:<EMAIL>"><EMAIL></a></p>
    </div>
    """,
    unsafe_allow_html=True,
)

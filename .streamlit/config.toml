# .streamlit/config.toml  –  Theme “KDP Stratega”
# .streamlit/config.toml  —  KDP Stratega theme
[theme]
base = "light"

# ─── Palette principale ─────────────────────────────────────────────
primaryColor = "#2F80ED"             # “Electric SaaS Blue” – call-to-action
linkColor = "#2F80ED"
backgroundColor = "#F2F4F7"          # Soft cloud grey (dashboard canvas)
secondaryBackgroundColor = "#FFFFFF" # Card & widget fill
codeBackgroundColor = "#EFF2F6"      # Slight tint for code
textColor = "#1E1E1E"                # Charcoal
borderColor = "#D9E0E8"              # Hair-line separators

# ─── Typography & sizing ────────────────────────────────────────────
font = "Inter, sans-serif"
headingFont = "Poppins, sans-serif"
codeFont = "monospace"
baseFontSize = 16
baseRadius = "8px"
showWidgetBorder = true
showSidebarBorder = false

# ─── Google Fonts (weight prefetch) ─────────────────────────────────
[[theme.fontFaces]]
family = "Inter"
url = "https://fonts.gstatic.com/s/inter/v12/Inter-VariableFont_slnt,wght.ttf"
weight = 400
style = "normal"

[[theme.fontFaces]]
family = "Poppins"
url = "https://fonts.gstatic.com/s/poppins/v20/Poppins-Bold.ttf"
weight = 700
style = "normal"

# ─── Sidebar override (coherent with main UI) ───────────────────────
[theme.sidebar]
backgroundColor = "#FFFFFF"
secondaryBackgroundColor = "#F7F9FB"
primaryColor = "#2F80ED"
linkColor = "#2F80ED"
textColor = "#1E1E1E"
borderColor = "#D9E0E8"
font = "Inter, sans-serif"
headingFont = "Poppins, sans-serif"
baseRadius = "8px"
showWidgetBorder = true

[client]
showSidebarNavigation = false

[server]
enableStaticServing = true

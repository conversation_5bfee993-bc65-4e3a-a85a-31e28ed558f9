import streamlit as st
from supabase import create_client, Client
from utils.config import config
import hashlib
import secrets
from typing import Optional, Dict, Any
import json
import streamlit.components.v1 as components
from datetime import datetime


class AuthManager:
    def __init__(self):
        self.conn = None
        self._init_connection()

    def _init_connection(self):
        """Initialize Supabase connection"""
        try:
            # Create direct Supabase client instead of using st.connection to prevent session bleeding
            self.conn = create_client(config.SUPABASE_URL, config.SUPABASE_KEY)
            
            # Restore session from session state if available
            if "supabase_session" in st.session_state:
                session = st.session_state["supabase_session"]
                if session and "access_token" in session and "refresh_token" in session:
                    try:
                        # Set the session on the new client
                        self.conn.auth.set_session(session["access_token"], session["refresh_token"])
                    except Exception as e:
                        # If session restore fails, continue without it
                        pass
            
            return self.conn
        except Exception as e:
            st.error(f"Failed to connect to database: {str(e)}")
            return None

    def get_current_user(self) -> Optional[Dict[str, Any]]:
        """Get current authenticated user from Supabase Auth"""
        if not self.conn:
            return None

        try:
            response = self.conn.auth.get_user()

            if response and hasattr(response, 'user') and response.user:
                # Convert Supabase User object to dict
                user = response.user
                return {
                    "id": user.id,
                    "email": user.email,
                    "user_metadata": getattr(user, 'user_metadata', {}),
                    "app_metadata": getattr(user, 'app_metadata', {}),
                    "created_at": getattr(user, 'created_at', None),
                    "updated_at": getattr(user, 'updated_at', None)
                }
            return None
        except Exception as e:
            # Token might be expired or invalid
            return None

    def get_session_token(self) -> Optional[str]:
        """Get current session refresh token for Stripe URLs"""
        if not self.conn:
            return None

        try:
            session = self.conn.auth.get_session()
            if session and hasattr(session, 'refresh_token'):
                return session.refresh_token
            return None
        except:
            return None

    def get_fresh_access_token_from_refresh(self, refresh_token: str) -> Optional[str]:
        """Get fresh access token from refresh token"""
        if not self.conn or not refresh_token:
            return None

        try:
            # Use refresh token to get fresh session
            response = self.conn.auth.refresh_session(refresh_token)
            if response and hasattr(response, 'session') and response.session:
                # Update stored session with new tokens
                st.session_state["supabase_session"] = {
                    "access_token": response.session.access_token,
                    "refresh_token": response.session.refresh_token
                }
                return response.session.access_token
            return None
        except Exception as e:
            st.error(f"Error refreshing token: {str(e)}")
            return None

    def restore_session_from_token(self, access_token: str) -> bool:
        """Restore session using access token from URL"""
        if not self.conn or not access_token:
            return False

        try:
            # Use the access token to get user info directly
            response = self.conn.auth.get_user(access_token)

            if response and hasattr(response, 'user') and response.user:
                user = response.user
                user_dict = {
                    "id": user.id,
                    "email": user.email,
                    "user_metadata": getattr(user, 'user_metadata', {}),
                    "app_metadata": getattr(user, 'app_metadata', {}),
                    "created_at": getattr(user, 'created_at', None),
                    "updated_at": getattr(user, 'updated_at', None)
                }

                # Force refresh user profile data from database
                user_profile = self.get_user_profile(user_dict["id"])

                # Force refresh vision status
                has_vision = self.check_vision_exists(user_dict["id"])

                if user_profile:
                    user_data = {
                        "id": user_dict["id"],
                        "email": user_dict["email"],
                        "name": user_profile.get("name", ""),
                        "credits": user_profile.get("credits", 100),
                        "onboarding_completed": user_profile.get("onboarding_completed", False),
                        "vision_completed": has_vision  # Use fresh check instead of cached value
                    }
                else:
                    # Create profile if it doesn't exist
                    user_metadata = user_dict.get("user_metadata", {})
                    fallback_data = {
                        "id": user_dict["id"],
                        "email": user_dict["email"],
                        "name": user_metadata.get("name", "") if user_metadata else "",
                        "credits": 100,
                        "onboarding_completed": False,
                        "vision_completed": has_vision  # Use fresh check
                    }

                    self.create_user_profile_if_missing(user_dict["id"], fallback_data)

                    # Try to get profile again after creation
                    user_profile = self.get_user_profile(user_dict["id"])
                    if user_profile:
                        user_data = {
                            "id": user_dict["id"],
                            "email": user_dict["email"],
                            "name": user_profile.get("name", ""),
                            "credits": user_profile.get("credits", 100),
                            "onboarding_completed": user_profile.get("onboarding_completed", False),
                            "vision_completed": has_vision
                        }
                    else:
                        user_data = fallback_data

                st.session_state["authenticated"] = True
                st.session_state["user_data"] = user_data
                return True

        except Exception as e:
            st.error(f"Error restoring session from token: {str(e)}")

        return False

    def refresh_user_data(self, user_id: str) -> bool:
        """Refresh user data from database and update session state"""
        try:
            # Get fresh user profile data
            user_profile = self.get_user_profile(user_id)

            # Get fresh vision status
            has_vision = self.check_vision_exists(user_id)

            if user_profile:
                # Update existing user data with fresh values
                current_user_data = st.session_state.get("user_data", {})

                updated_data = {
                    "name": user_profile.get("name", current_user_data.get("name", "")),
                    "credits": user_profile.get("credits", 100),
                    "onboarding_completed": user_profile.get("onboarding_completed", False),
                    "vision_completed": has_vision
                }

                current_user_data.update(updated_data)
                st.session_state["user_data"] = current_user_data

                return True
            else:
                # Create user profile if it doesn't exist
                current_user_data = st.session_state.get("user_data", {})
                self.create_user_profile_if_missing(user_id, current_user_data)

                # Try again after creating profile
                user_profile = self.get_user_profile(user_id)
                if user_profile:
                    updated_data = {
                        "name": user_profile.get("name", current_user_data.get("name", "")),
                        "credits": user_profile.get("credits", 100),
                        "onboarding_completed": user_profile.get("onboarding_completed", False),
                        "vision_completed": has_vision
                    }
                    current_user_data.update(updated_data)
                    st.session_state["user_data"] = current_user_data
                    return True
                else:
                    return False
        except Exception as e:
            pass

        return False

    def restore_session_from_supabase(self) -> bool:
        """Restore session from Supabase Auth if available"""
        user = self.get_current_user()
        if user:
            # Get user profile data
            user_profile = self.get_user_profile(user["id"])

            # Force refresh vision status
            has_vision = self.check_vision_exists(user["id"])

            if user_profile:
                user_data = {
                    "id": user["id"],
                    "email": user["email"],
                    "name": user_profile.get("name", ""),
                    "credits": user_profile.get("credits", 100),
                    "onboarding_completed": user_profile.get("onboarding_completed", False),
                    "vision_completed": has_vision
                }
            else:
                # Fallback if profile doesn't exist
                user_metadata = user.get("user_metadata", {})
                user_data = {
                    "id": user["id"],
                    "email": user["email"],
                    "name": user_metadata.get("name", "") if user_metadata else "",
                    "credits": 100,
                    "onboarding_completed": False,
                    "vision_completed": has_vision
                }

            st.session_state["authenticated"] = True
            st.session_state["user_data"] = user_data
            return True

        return False

    def hash_password(self, password: str) -> str:
        """Hash password with salt"""
        salt = secrets.token_hex(16)
        pwd_hash = hashlib.pbkdf2_hmac('sha256', password.encode('utf-8'), salt.encode('utf-8'), 100000)
        return salt + pwd_hash.hex()

    def verify_password(self, password: str, hashed: str) -> bool:
        """Verify password against hash"""
        try:
            salt = hashed[:32]
            stored_hash = hashed[32:]
            pwd_hash = hashlib.pbkdf2_hmac('sha256', password.encode('utf-8'), salt.encode('utf-8'), 100000)
            return pwd_hash.hex() == stored_hash
        except:
            return False


    def signup_user(self, email: str, password: str, name: str) -> Dict[str, Any]:
        """Register new user using Supabase Auth"""
        if not self.conn:
            return {"success": False, "message": "Database connection not available"}

        try:
            response = self.conn.auth.sign_up(
                {
                    "email": email,
                    "password": password,
                    "options": {
                        "data": {
                            "name": name
                        }
                    }
                }
            )

            if hasattr(response, 'user') and response.user:
                # Store session in session state for future connections
                if hasattr(response, 'session') and response.session:
                    st.session_state["supabase_session"] = {
                        "access_token": response.session.access_token,
                        "refresh_token": response.session.refresh_token
                    }
                # Automatically set session state after successful signup
                user_data = {
                    "id": response.user.id,
                    "email": response.user.email,
                    "name": name,
                    "credits": 100,
                    "onboarding_completed": False,
                    "vision_completed": False
                }
                st.session_state["authenticated"] = True
                st.session_state["user_data"] = user_data

                return {"success": True, "message": "Account created successfully", "user": user_data}
            else:
                return {"success": False, "message": "Failed to create account"}

        except Exception as e:
            return {"success": False, "message": f"Error: {str(e)}"}

    def login_user(self, email: str, password: str) -> Dict[str, Any]:
        """Login user using Supabase Auth"""
        if not self.conn:
            return {"success": False, "message": "Database connection not available"}

        try:
            response = self.conn.auth.sign_in_with_password(
                {"email": email, "password": password}
            )

            if hasattr(response, 'user') and response.user:
                # Store session in session state for future connections
                if hasattr(response, 'session') and response.session:
                    st.session_state["supabase_session"] = {
                        "access_token": response.session.access_token,
                        "refresh_token": response.session.refresh_token
                    }
                # Get user profile data
                user_profile = self.get_user_profile(response.user.id)

                if user_profile:
                    user_data = {
                        "id": response.user.id,
                        "email": response.user.email,
                        "name": user_profile.get("name", ""),
                        "credits": user_profile.get("credits", 100),
                        "onboarding_completed": user_profile.get("onboarding_completed", False),
                        "vision_completed": user_profile.get("vision_completed", False)
                    }
                else:
                    # Fallback if profile doesn't exist
                    user_data = {
                        "id": response.user.id,
                        "email": response.user.email,
                        "name": response.user.user_metadata.get("name", ""),
                        "credits": 100,
                        "onboarding_completed": False,
                        "vision_completed": False
                    }

                # Set session state immediately after successful login
                st.session_state["authenticated"] = True
                st.session_state["user_data"] = user_data

                return {"success": True, "user": user_data}
            else:
                return {"success": False, "message": "Invalid email or password"}

        except Exception as e:
            return {"success": False, "message": f"Error: {str(e)}"}

    def check_vision_exists(self, user_id: str) -> bool:
        """Check if user has completed vision builder"""
        if not self.conn:
            return False

        try:
            result = self.conn.table("vision_builder").select("id").eq("user_id", user_id).execute()
            return len(result.data) > 0
        except:
            return False

    def save_vision_builder(self, user_id: str, questions: list, answers: list, vision: str) -> bool:
        """Save vision builder data"""
        if not self.conn:
            st.error("Database connection not available")
            return False

        try:
            # Check if vision already exists
            existing = self.conn.table("vision_builder").select("id").eq("user_id", user_id).execute()

            vision_data = {
                "user_id": user_id,
                "question_1": questions[0] if len(questions) > 0 else "",
                "answer_1": answers[0] if len(answers) > 0 else "",
                "question_2": questions[1] if len(questions) > 1 else "",
                "answer_2": answers[1] if len(answers) > 1 else "",
                "question_3": questions[2] if len(questions) > 2 else "",
                "answer_3": answers[2] if len(answers) > 2 else "",
                "vision": vision
            }

            # Always try to delete existing first, then insert new
            # This avoids RLS issues with updates
            try:
                self.conn.table("vision_builder").delete().eq("user_id", user_id).execute()
            except:
                pass  # Ignore errors if no existing record

            # Insert the new vision data
            self.conn.table("vision_builder").insert(vision_data).execute()

            # Update user profile to mark vision as completed
            self.update_user_profile(user_id, {"vision_completed": True, "onboarding_completed": True})

            return True
        except Exception as e:
            st.error(f"Error saving vision: {str(e)}")
            return False

    def get_user_vision(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Get user's vision data"""
        if not self.conn:
            return None

        try:
            result = self.conn.table("vision_builder").select("*").eq("user_id", user_id).execute()
            if len(result.data) > 0:
                return result.data[0]
            return None
        except:
            return None

    def save_project(self, user_id: str, project_name: str, project_data: dict):
        """Save user project"""
        try:
            conn = self._init_connection()
            if not conn:
                return {"success": False, "error": "Database connection not available"}

            response = conn.table("projects").insert({
                "user_id": user_id,
                "project_name": project_name,
                "project_data": project_data,
                "status": "draft"
            }).execute()

            if response.data and len(response.data) > 0:
                project_id = response.data[0]["id"]
                return {"success": True, "project_id": project_id, "message": "Project saved successfully"}
            else:
                return {"success": False, "error": "Failed to save project"}

        except Exception as e:
            return {"success": False, "error": f"Error saving project: {str(e)}"}

    def get_user_projects(self, user_id: str):
        """Get user's projects"""
        if not self.conn:
            return None

        try:
            result = self.conn.table("projects").select("*").eq("user_id", user_id).order("created_at", desc=True).execute()
            return result.data
        except:
            return []

    def get_project_by_id(self, project_id: int):
        """Get a specific project by ID"""
        if not self.conn:
            return None

        try:
            result = self.conn.table("projects").select("*").eq("id", project_id).execute()
            if len(result.data) > 0:
                return result.data[0]
            return None
        except:
            return None

    def update_project(self, project_id: int, updates: dict) -> bool:
        """Update project data"""
        if not self.conn:
            return False

        try:
            self.conn.table("projects").update(updates).eq("id", project_id).execute()
            return True
        except Exception as e:
            st.error(f"Error updating project: {str(e)}")
            return False

    def get_current_user_project(self, user_id: str):
        """Get user's most recent project"""
        if not self.conn:
            return None

        try:
            result = self.conn.table("projects").select("*").eq("user_id", user_id).order("created_at", desc=True).limit(1).execute()
            if len(result.data) > 0:
                return result.data[0]
            return None
        except:
            return None



    def logout_user(self):
        """Logout current user"""
        if self.conn:
            try:
                self.conn.auth.sign_out()
            except:
                pass

        keys_to_remove = [
            "authenticated", "user_data", "current_step", "supabase_session",
            "argomento_keyword", "buyer_persona_generata", "titolo_scelto",
            "sottotitolo_scelto", "indice_libro_generato", "descrizione_amazon",
            "cover_suggerita", "posizionamento_editoriale", "recensioni_inserite",
            "report_recensioni", "analisi_argomento_generata", "amazon_books_data"
        ]

        for key in keys_to_remove:
            if key in st.session_state:
                del st.session_state[key]

    def require_auth(self):
        """Require authentication - restoration is handled in app.py"""
        # Simply check if authenticated, don't try to restore
        # (restoration is handled once in app.py to prevent session corruption)
        if not st.session_state.get("authenticated", False):
            st.error("Please login to access this page")
            st.switch_page("pages/auth_login.py")
            st.stop()

        return True

    def handle_post_signup_redirect(self, user_id: str):
        """Handle redirect after successful signup"""
        if user_id:
            has_vision = self.check_vision_exists(user_id)
            if not has_vision:
                st.session_state["redirect_to_vision"] = True
                st.session_state["signup_completed"] = True
            else:
                st.session_state["redirect_to_dashboard"] = True
        return True

    def handle_post_login_redirect(self, user_id: str):
        """Handle redirect after successful login"""
        if user_id:
            has_vision = self.check_vision_exists(user_id)
            if not has_vision:
                st.session_state["redirect_to_vision"] = True
            else:
                st.session_state["redirect_to_dashboard"] = True
        return True

    def create_user_profile_if_missing(self, user_id: str, user_data: Dict[str, Any]) -> bool:
        """Create user profile in database if it doesn't exist"""
        if not self.conn:
            return False

        try:
            profile_data = {
                "id": user_id,
                "email": user_data.get("email", ""),
                "name": user_data.get("name", ""),
                "credits": user_data.get("credits", 100),
                "onboarding_completed": user_data.get("onboarding_completed", False),
                "vision_completed": user_data.get("vision_completed", False)
            }

            self.conn.table("users").upsert(profile_data).execute()
            return True
        except Exception as e:
            return False

    def get_user_profile(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Get user profile data"""
        if not self.conn:
            return None

        try:
            result = self.conn.table("users").select("*").eq("id", user_id).execute()
            if len(result.data) > 0:
                return result.data[0]
            return None
        except:
            return None

    def update_user_profile(self, user_id: str, updates: Dict[str, Any]) -> bool:
        """Update user profile data"""
        if not self.conn:
            return False

        try:
            # Don't manually set updated_at - let the database handle it
            self.conn.table("users").update(updates).eq("id", user_id).execute()
            return True
        except Exception as e:
            st.error(f"Error updating user profile: {str(e)}")
            return False

    def deduct_credits(self, user_id: str, action_type: str = "other", amount: int = 1, project_id: int = None, description: str = None) -> bool: #type: ignore
        """Deduct credits from user account with usage tracking"""
        try:
            conn = self._init_connection()
            if not conn:
                st.error("Database connection failed")
                return False

            # Check current credits
            user_response = conn.table("users").select("credits").eq("id", user_id).execute()

            if not user_response.data:
                st.error("User not found")
                return False

            current_credits = user_response.data[0].get("credits", 0)

            if current_credits < amount:
                st.error(f"❌ Insufficient credits! You have {current_credits} but need {amount}")
                return False

            # Deduct credits
            new_credits = current_credits - amount
            update_response = conn.table("users").update({"credits": new_credits}).eq("id", user_id).execute()

            if not update_response.data:
                st.error("Failed to update credits")
                return False

            # Record usage
            usage_data = {
                "user_id": user_id,
                "action_type": action_type,
                "credits_used": amount,
                "project_id": project_id,
                "description": description
            }

            conn.table("credits_usage").insert(usage_data).execute()

            return True

        except Exception as e:
            st.error(f"Error deducting credits: {str(e)}")
            return False

    def add_user_credits(self, user_id: str, amount: int) -> bool:
        """Add credits to user account"""
        if not self.conn:
            return False

        try:
            # Get current credits
            user_profile = self.get_user_profile(user_id)
            if not user_profile:
                return False

            current_credits = user_profile.get("credits", 0)
            new_credits = current_credits + amount

            return self.update_user_profile(user_id, {"credits": new_credits})

        except Exception as e:
            st.error(f"Error adding credits: {str(e)}")
            return False



    def record_transaction(self, transaction_data: dict) -> bool:
        """Record a transaction in the database"""
        if not self.conn:
            return False

        try:
            self.conn.table("transactions").insert(transaction_data).execute()
            return True
        except Exception as e:
            st.error(f"Error recording transaction: {str(e)}")
            return False

    def get_user_transactions(self, user_id: str):
        """Get user's transaction history"""
        if not self.conn:
            return []

        try:
            result = self.conn.table("transactions").select("*").eq("user_id", user_id).order("created_at", desc=True).execute()
            return result.data
        except:
            return []

    def get_user_credits_usage(self, user_id: str):
        """Get user's credit usage summary"""
        if not self.conn:
            return []

        try:
            # Direct table query instead of RPC to avoid session corruption
            result = self.conn.table("credits_usage").select("action_type, credits_used").eq("user_id", user_id).execute()

            # Manually aggregate the data since we can't use RPC
            usage_summary = {}
            for record in result.data:
                action_type = record.get('action_type', 'unknown')
                credits_used = record.get('credits_used', 0)

                if action_type in usage_summary:
                    usage_summary[action_type] += credits_used
                else:
                    usage_summary[action_type] = credits_used

            # Convert to expected format
            summary_list = []
            for action_type, total_credits in usage_summary.items():
                summary_list.append({
                    'action_type': action_type,
                    'total_credits_used': total_credits
                })

            return summary_list
        except:
            return []

    def get_user_credits_history(self, user_id: str, limit: int = 50):
        """Get detailed credit usage history"""
        if not self.conn:
            return []

        try:
            result = self.conn.table("credits_usage").select("*").eq("user_id", user_id).order("created_at", desc=True).limit(limit).execute()
            return result.data
        except:
            return []

    def check_sufficient_credits(self, user_id: str, required_credits: int = 1) -> bool:
        """Check if user has sufficient credits for an action"""
        if not self.conn:
            return False

        try:
            user_profile = self.get_user_profile(user_id)
            if not user_profile:
                return False

            current_credits = user_profile.get("credits", 0)
            return current_credits >= required_credits

        except:
            return False

    def delete_project(self, project_id, user_id):
        """Delete a project for the authenticated user"""
        try:
            conn = self._init_connection()
            if not conn:
                return {"success": False, "error": "Database connection failed"}

            # First verify the project belongs to the user
            response = conn.table("projects").select("*").eq("id", project_id).eq("user_id", user_id).execute()

            if not response.data:
                return {"success": False, "error": "Project not found or access denied"}

            project_exists_before = len(response.data) > 0

            # Delete the project
            delete_response = conn.table("projects").delete().eq("id", project_id).eq("user_id", user_id).execute()

            # Verify the deletion actually worked by checking if record still exists
            verify_response = conn.table("projects").select("*").eq("id", project_id).eq("user_id", user_id).execute()
            project_still_exists = len(verify_response.data) > 0

            if project_still_exists:
                return {"success": False, "error": "Delete operation failed - project still exists. Check RLS policies."}

            return {"success": True, "message": "Project deleted successfully"}

        except Exception as e:
            st.error(f"🔍 DEBUG - Delete exception: {str(e)}")
            return {"success": False, "error": f"Error deleting project: {str(e)}"}

    def update_project_step(self, project_id, user_id, step_number, step_data):
        """Update a specific step's data in a project"""
        try:
            conn = self._init_connection()
            if not conn:
                return {"success": False, "error": "Database connection failed"}

            # Get current project
            response = conn.table("projects").select("*").eq("id", project_id).eq("user_id", user_id).execute()

            if not response.data:
                return {"success": False, "error": "Project not found or access denied"}

            project = response.data[0]
            project_data = project.get("project_data", {})

            # Initialize step_data if it doesn't exist
            if "step_data" not in project_data:
                project_data["step_data"] = {}

            # Update the specific step data
            project_data["step_data"][f"step{step_number}"] = step_data
            project_data["current_step"] = step_number + 1
            project_data["last_step_completed"] = step_number

            # Update the project
            update_data = {
                "project_data": project_data,
                "status": "in_progress" if step_number < 10 else "completed"
            }

            update_response = conn.table("projects").update(update_data).eq("id", project_id).eq("user_id", user_id).execute()

            return {"success": True, "message": "Step data updated successfully"}

        except Exception as e:
            return {"success": False, "error": f"Error updating step data: {str(e)}"}

    def get_project_step_data(self, project_id, user_id, step_number):
        """Get specific step data from a project"""
        try:
            conn = self._init_connection()
            if not conn:
                return {"success": False, "error": "Database connection failed"}

            # Get current project
            response = conn.table("projects").select("*").eq("id", project_id).eq("user_id", user_id).execute()

            if not response.data:
                return {"success": False, "error": "Project not found or access denied"}

            project = response.data[0]
            project_data = project.get("project_data", {})
            step_data = project_data.get("step_data", {}).get(f"step{step_number}", {})

            return {"success": True, "data": step_data}

        except Exception as e:
            return {"success": False, "error": f"Error getting step data: {str(e)}"}

    def save_step_data(self, user_id, project_id, step_number, step_data):
        """Save step data to project in a modular way"""
        try:
            conn = self._init_connection()
            if not conn:
                return {"success": False, "error": "Database connection failed"}

            # Get current project
            response = conn.table("projects").select("*").eq("id", project_id).eq("user_id", user_id).execute()

            if not response.data:
                return {"success": False, "error": "Project not found or access denied"}

            project = response.data[0]
            project_data = project.get("project_data", {})

            # Initialize step_data if it doesn't exist
            if "step_data" not in project_data:
                project_data["step_data"] = {}

            # Update the specific step data
            project_data["step_data"][f"step{step_number}"] = {
                **step_data,
                "completed": True,
                "timestamp": datetime.now().isoformat()
            }

            # Update current step and progress
            project_data["current_step"] = max(step_number + 1, project_data.get("current_step", 1))
            project_data["last_step_completed"] = max(step_number, project_data.get("last_step_completed", 0))

            # Determine status
            status = "completed" if step_number >= 10 else "in_progress"

            # Prepare update data - only include columns that exist
            update_data = {
                "project_data": project_data,
                "status": status
            }

            update_response = conn.table("projects").update(update_data).eq("id", project_id).eq("user_id", user_id).execute()

            return {"success": True, "message": "Step data saved successfully"}

        except Exception as e:
            return {"success": False, "error": f"Error saving step data: {str(e)}"}

# Create auth manager instance per request to avoid session bleeding
def get_auth_manager():
    """Get a fresh auth manager instance for each request"""
    return AuthManager()



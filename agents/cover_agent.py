import openai


def genera_suggerimenti_cover(dati, openai_key, prompt, lingua="italiano"):
    if not openai_key:
        raise ValueError("❌ Chiave OpenAI mancante.")
    prompt_finale = f"""{prompt}

📘 Informazioni principali:
- Argomento: {dati.get('Argomento', 'N/D')}
- Posizionamento: {dati.get('Posizionamento', 'N/D')}
- Target: {dati.get('Buyer Persona', 'N/D')}
- <PERSON><PERSON>: {dati.get('Titolo', '')}
- Sottotito<PERSON>: {dati.get('Sottotitolo', '')}
- Formato: {dati.get('Formato', 'Non definito')}
- Ispirazioni visive: {dati.get('Ispirazioni', 'Nessuna')}

🎯 Obiettivo:
Suggerisci 3 idee visive per la copertina di un libro da pubblicare su Amazon KDP.
Ogni idea deve includere:
- Un concetto visivo
- Una palette colori coerente
- Font suggeriti e stile illustrativo
- Esempi o riferimenti (link Amazon se disponibili)

📌 Scrivi in {lingua}, in formato elenco puntato, con tono professionale.
"""
    try:
        client = openai.OpenAI(api_key=openai_key)
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {
                    "role": "system",
                    "content": "Sei un art director editoriale specializzato in copertine di libri per Amazon KDP.",
                },
                {"role": "user", "content": prompt_finale},
            ],
            temperature=0.75,
            max_tokens=1100,
        )
        return (
            response.choices[0].message.content.strip()
            if response.choices[0].message.content
            else ""
        )
    except Exception as e:
        raise RuntimeError(f"Errore durante la generazione suggerimenti cover: {e}")

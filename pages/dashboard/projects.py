from openai import images
import streamlit as st
from utils.auth import get_auth_manager
import json
import requests
from datetime import datetime
from utils.credits import get_user_credits, get_user_id

# Require authentication (restoration handled in app.py)
get_auth_manager().require_auth()


# Header

# Header
st.markdown(
    f"""
    <h1 style='text-align: center; color: #222; font-size: 2.5em;'>📚 I tuoi libri</h1>
    <h3 style='text-align: center; color: #444;'>Gestisci i tuoi progetti e visualizza il loro stato</h3>
    """,
    unsafe_allow_html=True,
)


# Always load projects fresh to ensure latest data
user_id = get_user_id()
projects = get_auth_manager().get_user_projects(user_id)


# Project stats
if projects:
    total_projects = len(projects)
    completed_projects = len([p for p in projects if p.get("status") == "completed"])
    in_progress_projects = len(
        [p for p in projects if p.get("status") == "in_progress"]
    )
    draft_projects = len([p for p in projects if p.get("status") == "draft"])

    # Calculate average progress using last_step_completed
    total_progress = 0
    for p in projects:
        project_data = p.get("project_data", {})
        last_step = project_data.get("last_step_completed", 0)
        project_progress = int((last_step / 11) * 100) if last_step > 0 else 0
        total_progress += project_progress
    avg_progress = int(total_progress / total_projects) if total_projects > 0 else 0

    # Quick stats
    col1, col2, col3, col4, col5 = st.columns(5)

    with col1:
        st.metric("📚 Progetti totali", total_projects)

    with col2:
        st.metric("✅ Completati", completed_projects)

    with col3:
        st.metric("🚧 In corso", in_progress_projects)

    with col4:
        st.metric("📝 Drafts", draft_projects)

    with col5:
        st.metric("📊 Progresso medio", f"{avg_progress}%")

    # Progress visualization
    if total_projects > 0:
        st.markdown("### 📈 Stato avanzamento")
        progress_ranges = {
            "🟢 Completo (100%)": 0,
            "🟡 Avanzato (75-99%)": 0,
            "🟠 Moderato (50-74%)": 0,
            "🔴 Iniziato (0-49%)": 0,
        }

        for p in projects:
            project_data = p.get("project_data", {})
            last_step = project_data.get("last_step_completed", 0)
            project_progress = int((last_step / 11) * 100) if last_step > 0 else 0

            if project_progress == 100:
                progress_ranges["🟢 Completo (100%)"] += 1
            elif 75 <= project_progress < 100:
                progress_ranges["🟡 Avanzato (75-99%)"] += 1
            elif 50 <= project_progress < 75:
                progress_ranges["🟠 Moderato (50-74%)"] += 1
            else:
                progress_ranges["🔴 Iniziato (0-49%)"] += 1

        for label, count in progress_ranges.items():
            if count > 0:
                st.text(f"{label}: {count} projects")

    st.markdown("---")

# Quick actions
col1, col2 = st.columns([2, 1])

with col1:
    if st.button(
        "📚 Crea nuovo progetto di libro", type="primary", use_container_width=True
    ):
        # Clear any existing project data and set new project flag
        st.session_state["creating_new_project"] = True
        st.session_state["from_projects_dashboard"] = True
        # Clear existing project info
        for key in ["current_project_id", "current_project_name", "current_book_topic"]:
            if key in st.session_state:
                del st.session_state[key]
        st.switch_page("pages/step1_home.py")

with col2:
    if st.button("📊 Analytics", use_container_width=True):
        if projects:
            st.info("Analytics: Coming soon!")
        else:
            st.warning("Crea un progetto di libro prima di vedere le analitiche")

st.markdown("---")

# Filter and sort options
col1, col2, col3 = st.columns(3)

with col1:
    status_filter = st.selectbox(
        "Filter by Status",
        ["All", "Draft", "In Progress", "Completed", "Archived"],
        key="status_filter",
    )

with col2:
    sort_by = st.selectbox(
        "Sort by",
        ["Last Updated", "Created Date", "Project Name", "Status"],
        key="sort_by",
    )

with col3:
    sort_order = st.selectbox("Order", ["Descending", "Ascending"], key="sort_order")


# # Apply filters and sorting
if projects:
    filtered_projects = projects

    # Apply status filter
    if status_filter != "All":
        filtered_projects = [
            p
            for p in filtered_projects
            if p.get("status", "draft").lower() == status_filter.lower()
        ]

    # Apply sorting
    if sort_by == "Last Updated":
        filtered_projects.sort(
            key=lambda x: x.get("updated_at", ""), reverse=(sort_order == "Descending")
        )
    elif sort_by == "Created Date":
        filtered_projects.sort(
            key=lambda x: x.get("created_at", ""), reverse=(sort_order == "Descending")
        )
    elif sort_by == "Project Name":
        filtered_projects.sort(
            key=lambda x: x.get("project_name", ""),
            reverse=(sort_order == "Descending"),
        )
    elif sort_by == "Status":
        filtered_projects.sort(
            key=lambda x: x.get("status", ""), reverse=(sort_order == "Descending")
        )

    st.markdown(f"### 📋 Progetti ({len(filtered_projects)} trovati")

    if filtered_projects:
        # Display projects in 2-column layout
        for i in range(0, len(filtered_projects), 3):
            cols = st.columns(3)

            for j, col in enumerate(cols):
                if i + j < len(filtered_projects):
                    project = filtered_projects[i + j]

                    with col:

                        # Create card container with fixed height styling
                        st.markdown(
                            '<div class="project-card-wrapper">', unsafe_allow_html=True
                        )
                        with st.container(border=True):
                            project_name = project.get(
                                "project_name", "Untitled Project"
                            )
                            project_title = project.get(
                                "project_title", "Untitled Project"
                            )
                            project_status = project.get("status", "draft")

                            # Calculate progress from project_data.last_step_completed
                            project_data = project.get("project_data", {})
                            last_step = project_data.get("last_step_completed", 0)
                            progress = (
                                int((last_step / 11) * 100) if last_step > 0 else 0
                            )

                            created_at = project.get("created_at", "Unknown")
                            updated_at = project.get("updated_at", "Unknown")

                            # Format dates
                            try:
                                if created_at != "Unknown":
                                    created_date = datetime.fromisoformat(
                                        created_at.replace("Z", "+00:00")
                                    ).strftime("%Y-%m-%d")
                                else:
                                    created_date = "Unknown"

                                if updated_at != "Unknown":
                                    updated_date = datetime.fromisoformat(
                                        updated_at.replace("Z", "+00:00")
                                    ).strftime("%Y-%m-%d %H:%M")
                                else:
                                    updated_date = "Unknown"
                            except:
                                created_date = "Unknown"
                                updated_date = "Unknown"

                            # Main content row: cover image and details
                            cover_col, content_col = st.columns([1, 3])

                            with cover_col:
                                # Cover image
                                cover_url = project.get("cover_image_url")
                                if cover_url:
                                    try:
                                        st.image(cover_url, width=200)
                                    except:
                                        st.markdown(
                                            '<img src="/app/static/placeholder_cover.png" width="200">',
                                            unsafe_allow_html=True,
                                        )

                                else:
                                    st.markdown(
                                        '<img src="/app/static/placeholder_cover.png" width="200">',
                                        unsafe_allow_html=True,
                                    )

                            with content_col:
                                # Extract book title based on project type
                                if (
                                    project_data.get("project_type")
                                    == "editorial_series"
                                ):
                                    editorial_roadmap = project_data.get(
                                        "editorial_roadmap", {}
                                    )
                                    recommended_order = editorial_roadmap.get(
                                        "recommended_order", []
                                    )
                                    current_book_index = project_data.get(
                                        "current_book_index", 0
                                    )

                                    if recommended_order and current_book_index < len(
                                        recommended_order
                                    ):
                                        book_title = recommended_order[
                                            current_book_index
                                        ].get("topic", project_name)
                                    else:
                                        book_title = project_name
                                else:
                                    # For standalone books, check if we have book topic in step data
                                    step_data = project_data.get("step_data", {})
                                    if step_data.get("step2", {}).get(
                                        "argomento_keyword"
                                    ):
                                        book_title = step_data["step2"][
                                            "argomento_keyword"
                                        ]
                                    else:
                                        book_title = project_name

                                # Display book title
                                st.markdown(f"**📖 {book_title}**")

                                # Status badge
                                status_colors = {
                                    "draft": "#ffa500",
                                    "in_progress": "#1f77b4",
                                    "completed": "#2ca02c",
                                    "archived": "#d62728",
                                }
                                status_color = status_colors.get(
                                    project_status, "#666666"
                                )

                                st.markdown(
                                    f"""
                                <span style="
                                    background-color: {status_color};
                                    color: white;
                                    padding: 3px 8px;
                                    border-radius: 12px;
                                    font-size: 11px;
                                    font-weight: bold;
                                ">{project_status.title()}</span>
                                """,
                                    unsafe_allow_html=True,
                                )

                                # Display project name only if different from book title
                                if book_title != project_name:
                                    st.caption(f"Progetto: {project_name}")

                                st.markdown(f"**Created:** {created_date}")
                                st.markdown(f"**Updated:** {updated_date}")

                            # Progress bar with proper percentage display
                            progress_value = (
                                max(0, min(100, progress)) / 100
                            )  # Ensure 0-1 range
                            st.progress(progress_value, text=f"Progress: {progress}%")

                            # Action buttons
                            btn_col1, btn_col2, btn_col3 = st.columns(3)

                            with btn_col1:
                                if st.button(
                                    "📂 Open",
                                    key=f"open_{project['id']}",
                                    use_container_width=True,
                                ):
                                    # Load project data into session state
                                    try:
                                        project_data = project.get("project_data", {})

                                        # Set current project info
                                        st.session_state["current_project_id"] = (
                                            project["id"]
                                        )
                                        st.session_state["current_project_name"] = (
                                            project_name
                                        )

                                        # Handle editorial series projects
                                        if (
                                            project_data.get("project_type")
                                            == "editorial_series"
                                        ):
                                            editorial_roadmap = project_data.get(
                                                "editorial_roadmap", {}
                                            )
                                            recommended_order = editorial_roadmap.get(
                                                "recommended_order", []
                                            )
                                            current_book_index = project_data.get(
                                                "current_book_index", 0
                                            )

                                            if (
                                                recommended_order
                                                and current_book_index
                                                < len(recommended_order)
                                            ):
                                                current_book = recommended_order[
                                                    current_book_index
                                                ]
                                                current_book_topic = current_book.get(
                                                    "topic", ""
                                                )
                                                st.session_state[
                                                    "current_book_topic"
                                                ] = current_book_topic
                                                st.session_state[
                                                    "editorial_series_context"
                                                ] = {
                                                    "total_books": len(
                                                        recommended_order
                                                    ),
                                                    "current_book_index": current_book_index,
                                                    "current_book_title": current_book_topic,
                                                    "roadmap": recommended_order,
                                                }
                                            else:
                                                st.session_state[
                                                    "current_book_topic"
                                                ] = (
                                                    project_data.get(
                                                        "book_topics", [""]
                                                    )[0]
                                                    if project_data.get("book_topics")
                                                    else ""
                                                )
                                        else:
                                            # Check for book topic in multiple places
                                            book_topic = project_data.get(
                                                "book_topic", ""
                                            )  # First check root level
                                            if not book_topic:
                                                book_topic = (
                                                    project_data.get("step_data", {})
                                                    .get("step2", {})
                                                    .get("argomento_keyword", "")
                                                )
                                            st.session_state["current_book_topic"] = (
                                                book_topic
                                            )

                                        # Set current step from project data
                                        current_step = project_data.get(
                                            "current_step", 1
                                        )
                                        last_completed = project_data.get(
                                            "last_step_completed", 0
                                        )
                                        st.session_state["current_step"] = current_step
                                        st.session_state["last_step_completed"] = (
                                            last_completed
                                        )

                                        # Load all step data into session state
                                        step_data = project_data.get("step_data", {})

                                        # Step 1 data
                                        if "step1" in step_data:
                                            st.session_state["marketplace"] = step_data[
                                                "step1"
                                            ].get("marketplace", "IT")
                                            st.session_state["lingua_target"] = (
                                                step_data["step1"].get(
                                                    "lingua_target", "it"
                                                )
                                            )

                                        # Step 2 data
                                        if "step2" in step_data:
                                            st.session_state["argomento_keyword"] = (
                                                step_data["step2"].get(
                                                    "argomento_keyword", ""
                                                )
                                            )
                                            st.session_state[
                                                "analisi_argomento_generata"
                                            ] = step_data["step2"].get(
                                                "analisi_argomento_generata", ""
                                            )
                                            st.session_state["amazon_books_data"] = (
                                                step_data["step2"].get(
                                                    "amazon_books_data", []
                                                )
                                            )
                                            st.session_state["amazon_books"] = (
                                                step_data["step2"].get(
                                                    "amazon_books", ""
                                                )
                                            )
                                        # Also ensure argomento_keyword is set from book topic if available
                                        elif st.session_state.get("current_book_topic"):
                                            st.session_state["argomento_keyword"] = (
                                                st.session_state["current_book_topic"]
                                            )

                                        # Step 3 data
                                        if "step3" in step_data:
                                            st.session_state["recensioni_inserite"] = (
                                                step_data["step3"].get(
                                                    "recensioni_inserite", []
                                                )
                                            )
                                            st.session_state["report_recensioni"] = (
                                                step_data["step3"].get(
                                                    "report_recensioni", ""
                                                )
                                            )
                                            st.session_state["problemi_recensioni"] = (
                                                step_data["step3"].get(
                                                    "problemi_recensioni", ""
                                                )
                                            )
                                            st.session_state[
                                                "recensioni_analizzate"
                                            ] = step_data["step3"].get(
                                                "recensioni_analizzate", ""
                                            )

                                        # Step 4 data
                                        if "step4" in step_data:
                                            st.session_state[
                                                "buyer_persona_generata"
                                            ] = step_data["step4"].get(
                                                "buyer_persona_generata", ""
                                            )

                                        # Step 5 data
                                        if "step5" in step_data:
                                            st.session_state[
                                                "posizionamento_editoriale"
                                            ] = step_data["step5"].get(
                                                "posizionamento_editoriale", ""
                                            )

                                        # Step 6 data
                                        if "step6" in step_data:
                                            st.session_state["titolo_scelto"] = (
                                                step_data["step6"].get(
                                                    "titolo_scelto", ""
                                                )
                                            )
                                            st.session_state["sottotitolo_scelto"] = (
                                                step_data["step6"].get(
                                                    "sottotitolo_scelto", ""
                                                )
                                            )
                                            st.session_state[
                                                "titoli_sottotitoli_generati"
                                            ] = step_data["step6"].get(
                                                "titoli_sottotitoli_generati", ""
                                            )

                                        # Step 7 data
                                        if "step7" in step_data:
                                            st.session_state["idea_analysis"] = (
                                                step_data["step7"].get(
                                                    "idea_analysis", {}
                                                )
                                            )

                                        # Step 8 data
                                        if "step8" in step_data:
                                            st.session_state[
                                                "indice_libro_generato"
                                            ] = step_data["step8"].get(
                                                "indice_libro_generato", ""
                                            )

                                        # Step 9a data
                                        if "step9a" in step_data:
                                            st.session_state["descrizione_amazon"] = (
                                                step_data["step9a"].get(
                                                    "descrizione_amazon", ""
                                                )
                                            )

                                        # Step 10 data
                                        if "step10" in step_data:
                                            st.session_state["cover_suggerita"] = (
                                                step_data["step10"].get(
                                                    "cover_suggerita", ""
                                                )
                                            )
                                            st.session_state[
                                                "selected_cover_idea_content"
                                            ] = step_data["step10"].get(
                                                "selected_cover_idea", ""
                                            )
                                            st.session_state[
                                                "selected_cover_idea_index"
                                            ] = step_data["step10"].get(
                                                "selected_cover_idea_index", 0
                                            )

                                        # Step 10b data (cover URL)
                                        if "step10b" in step_data:
                                            st.session_state["cover_url"] = step_data[
                                                "step10b"
                                            ].get("cover_url", "")
                                            st.session_state["cover_image_url"] = (
                                                step_data["step10b"].get(
                                                    "cover_image_url", ""
                                                )
                                            )
                                            st.session_state["design_method"] = (
                                                step_data["step10b"].get(
                                                    "design_method", ""
                                                )
                                            )
                                            st.session_state[
                                                "cover_design_completed"
                                            ] = step_data["step10b"].get(
                                                "cover_design_completed", False
                                            )
                                            st.session_state["cover_skipped"] = (
                                                step_data["step10b"].get(
                                                    "cover_skipped", False
                                                )
                                            )
                                            # Also load cover suggestions from step10b
                                            cover_suggestions = step_data[
                                                "step10b"
                                            ].get("cover_suggerita", "")
                                            if cover_suggestions:
                                                st.session_state["cover_suggerita"] = (
                                                    cover_suggestions
                                                )
                                            selected_idea = step_data["step10b"].get(
                                                "selected_cover_idea", ""
                                            )
                                            if selected_idea:
                                                st.session_state[
                                                    "selected_cover_idea_content"
                                                ] = selected_idea

                                        # Also check for cover_image_url at project level
                                        if project.get("cover_image_url"):
                                            st.session_state["cover_image_url"] = (
                                                project["cover_image_url"]
                                            )

                                        # Store step_data in session state for access in other pages
                                        st.session_state["step_data"] = step_data

                                        # Step 11 data (backup for cover data)
                                        if "step11" in step_data:
                                            summary_data = step_data["step11"].get(
                                                "summary_data", {}
                                            )
                                            # Only override if step10 data is empty
                                            if not st.session_state.get(
                                                "cover_suggerita"
                                            ):
                                                st.session_state["cover_suggerita"] = (
                                                    summary_data.get(
                                                        "cover_suggestion", ""
                                                    )
                                                )

                                        # Load additional session state data for all completed steps
                                        for step_num in range(1, current_step):
                                            step_key = f"step{step_num}"
                                            if step_key in step_data:
                                                step_info = step_data[step_key]

                                                # Load step-specific data into session state
                                                if step_num == 1:
                                                    st.session_state["marketplace"] = (
                                                        step_info.get(
                                                            "marketplace", "IT"
                                                        )
                                                    )
                                                    st.session_state[
                                                        "lingua_target"
                                                    ] = step_info.get(
                                                        "lingua_target", "it"
                                                    )
                                                elif step_num == 2:
                                                    st.session_state[
                                                        "argomento_keyword"
                                                    ] = step_info.get(
                                                        "argomento_keyword", ""
                                                    )
                                                    st.session_state[
                                                        "analisi_argomento_generata"
                                                    ] = step_info.get(
                                                        "analisi_argomento_generata", ""
                                                    )
                                                    st.session_state[
                                                        "amazon_books_data"
                                                    ] = step_info.get(
                                                        "amazon_books_data", []
                                                    )
                                                    st.session_state["amazon_books"] = (
                                                        step_info.get(
                                                            "amazon_books", ""
                                                        )
                                                    )
                                                elif step_num == 3:
                                                    st.session_state[
                                                        "recensioni_inserite"
                                                    ] = step_info.get(
                                                        "recensioni_inserite", []
                                                    )
                                                    st.session_state[
                                                        "report_recensioni"
                                                    ] = step_info.get(
                                                        "report_recensioni", ""
                                                    )
                                                elif step_num == 4:
                                                    st.session_state[
                                                        "buyer_persona_generata"
                                                    ] = step_info.get(
                                                        "buyer_persona_generata", ""
                                                    )
                                                elif step_num == 5:
                                                    st.session_state[
                                                        "posizionamento_editoriale"
                                                    ] = step_info.get(
                                                        "posizionamento_editoriale", ""
                                                    )
                                                elif step_num == 6:
                                                    st.session_state[
                                                        "titolo_scelto"
                                                    ] = step_info.get(
                                                        "titolo_scelto", ""
                                                    )
                                                    st.session_state[
                                                        "sottotitolo_scelto"
                                                    ] = step_info.get(
                                                        "sottotitolo_scelto", ""
                                                    )
                                                    st.session_state[
                                                        "titoli_sottotitoli_generati"
                                                    ] = step_info.get(
                                                        "titoli_sottotitoli_generati",
                                                        "",
                                                    )
                                                elif step_num == 7:
                                                    st.session_state[
                                                        "idea_analysis"
                                                    ] = step_info.get(
                                                        "idea_analysis", {}
                                                    )
                                                elif step_num == 8:
                                                    st.session_state[
                                                        "indice_libro_generato"
                                                    ] = step_info.get(
                                                        "indice_libro_generato", ""
                                                    )
                                                elif step_num == 9:
                                                    st.session_state[
                                                        "descrizione_amazon"
                                                    ] = step_info.get(
                                                        "descrizione_amazon", ""
                                                    )
                                                elif step_num == 10:
                                                    st.session_state[
                                                        "cover_suggerita"
                                                    ] = step_info.get(
                                                        "cover_suggerita", ""
                                                    )
                                                    st.session_state[
                                                        "selected_cover_idea_content"
                                                    ] = step_info.get(
                                                        "selected_cover_idea", ""
                                                    )
                                                    st.session_state[
                                                        "selected_cover_idea_index"
                                                    ] = step_info.get(
                                                        "selected_cover_idea_index", 0
                                                    )
                                                elif step_num == 11:
                                                    # Step 10b data might be stored here
                                                    if "cover_url" in step_info:
                                                        st.session_state[
                                                            "cover_url"
                                                        ] = step_info.get(
                                                            "cover_url", ""
                                                        )

                                        # Redirect to the appropriate step
                                        step_pages = {
                                            1: "pages/step1_home.py",
                                            2: "pages/step2_argomento_keyword.py",
                                            3: "pages/step3_recensioni.py",
                                            4: "pages/step4_buyer_persona.py",
                                            5: "pages/step5_posizionamento.py",
                                            6: "pages/step6_titolo_sottotitolo.py",
                                            7: "pages/step7_idea_analyzer.py",
                                            8: "pages/step8_indice_perfetto.py",
                                            9: "pages/step9_descrizione_amazon.py",
                                            10: "pages/step10_cover_perfetta.py",
                                            11: "pages/step10b_design_cover.py",
                                            12: "pages/step11_riepilogo.py",
                                            13: "pages/step12_scarica_word.py",
                                        }

                                        target_page = step_pages.get(
                                            current_step, "pages/step11_scarica_word.py"
                                        )

                                        # Enhanced success message for editorial series
                                        if (
                                            project_data.get("project_type")
                                            == "editorial_series"
                                        ):
                                            editorial_context = st.session_state.get(
                                                "editorial_series_context", {}
                                            )
                                            current_book_title = editorial_context.get(
                                                "current_book_title", ""
                                            )
                                            current_book_num = (
                                                editorial_context.get(
                                                    "current_book_index", 0
                                                )
                                                + 1
                                            )
                                            total_books = editorial_context.get(
                                                "total_books", 1
                                            )

                                            st.success(
                                                f"✅ Serie Editoriale '{project_name}' caricata! Lavorando su Libro {current_book_num}/{total_books}: '{current_book_title}' - Step {current_step} ({progress}% completato)"
                                            )
                                        else:
                                            st.success(
                                                f"✅ Progetto '{project_name}' caricato! Proseguendo dal Step {current_step} ({progress}% completato)"
                                            )

                                        st.switch_page(target_page)

                                    except Exception as e:
                                        st.error(f"Error loading project: {str(e)}")

                            with btn_col2:
                                if project_status == "completed":
                                    st.write("")  # Empty space for completed projects
                                else:
                                    st.write("")  # Empty space

                            with btn_col3:
                                if project_status == "completed":
                                    # Check for document URL in project data
                                    doc_url = None
                                    if (
                                        project_data.get("project_type")
                                        == "editorial_series"
                                    ):
                                        # For editorial series, check current book's document URL
                                        books_status = project_data.get(
                                            "books_status", []
                                        )
                                        current_book_index = project_data.get(
                                            "current_book_index", 0
                                        )
                                        if current_book_index < len(books_status):
                                            doc_url = books_status[
                                                current_book_index
                                            ].get("document_url")
                                    else:
                                        # For standalone books, check step12 data
                                        step12_data = project_data.get(
                                            "step_data", {}
                                        ).get("step12", {})
                                        doc_url = step12_data.get("document_url")

                                    if doc_url:
                                        st.markdown(
                                            f"📥 [Download Document]({doc_url})",
                                            unsafe_allow_html=True,
                                        )
                                    else:
                                        if st.button(
                                            "📥 Export",
                                            key=f"export_{project['id']}",
                                            use_container_width=True,
                                        ):
                                            st.info(
                                                "Document not available. Complete Step 12 to generate it."
                                            )
                                else:
                                    if st.button(
                                        "🗑️ Delete",
                                        key=f"delete_{project['id']}",
                                        type="secondary",
                                        use_container_width=True,
                                    ):
                                        st.session_state[
                                            f"confirm_delete_{project['id']}"
                                        ] = True
                                        st.rerun()

                            # Add PlottyBot button for completed projects

                            if st.button(
                                "🤖 Scrivilo con PlottyBot",
                                key=f"plottybot_{project['id']}",
                                type="primary",
                                use_container_width=True,
                            ):
                                plottybot_url = "https://plottybot.com/?link=161"
                                st.markdown(
                                    f'<meta http-equiv="refresh" content="0; url={plottybot_url}">',
                                    unsafe_allow_html=True,
                                )
                                st.success("✅ Apertura PlottyBot...")

                            # Delete confirmation
                            if st.session_state.get(
                                f"confirm_delete_{project['id']}", False
                            ):
                                st.markdown("---")
                                st.error(
                                    f"⚠️ Sei sicuro di voler eliminare **{project_name}**?"
                                )

                                confirm_col1, confirm_col2 = st.columns(2)

                                with confirm_col1:
                                    if st.button(
                                        "✅ Yes, Delete",
                                        key=f"confirm_yes_{project['id']}",
                                        type="primary",
                                        use_container_width=True,
                                    ):
                                        user_id = get_user_id()
                                        result = get_auth_manager().delete_project(
                                            project["id"], user_id
                                        )

                                        if result["success"]:
                                            st.success("Libro cancellato con successo!")
                                            # Clear confirmation state
                                            if (
                                                f"confirm_delete_{project['id']}"
                                                in st.session_state
                                            ):
                                                del st.session_state[
                                                    f"confirm_delete_{project['id']}"
                                                ]
                                            st.rerun()
                                        else:
                                            st.error(
                                                f"Failed to delete project: {result.get('error', 'Unknown error')}"
                                            )

                                with confirm_col2:
                                    if st.button(
                                        "❌ Cancella",
                                        key=f"confirm_no_{project['id']}",
                                        use_container_width=True,
                                    ):
                                        if (
                                            f"confirm_delete_{project['id']}"
                                            in st.session_state
                                        ):
                                            del st.session_state[
                                                f"confirm_delete_{project['id']}"
                                            ]
                                        st.rerun()

                            # Final results section for completed projects
                            if project_status == "completed":
                                final_result = project.get("final_result")
                                if final_result:
                                    if st.button(
                                        f"🎉 Vedi Risultato",
                                        key=f"results_{project['id']}",
                                        use_container_width=True,
                                    ):
                                        st.session_state[
                                            f"show_results_{project['id']}"
                                        ] = not st.session_state.get(
                                            f"show_results_{project['id']}", False
                                        )

                                    if st.session_state.get(
                                        f"show_results_{project['id']}", False
                                    ):
                                        st.markdown("---")
                                        try:
                                            if isinstance(final_result, str):
                                                result_data = json.loads(final_result)
                                            else:
                                                result_data = final_result

                                            result_col1, result_col2 = st.columns(2)

                                            with result_col1:
                                                if "title" in result_data:
                                                    st.markdown(
                                                        f"**📝 Title:** {result_data['title']}"
                                                    )
                                                if "subtitle" in result_data:
                                                    st.markdown(
                                                        f"**✨ Subtitle:** {result_data['subtitle']}"
                                                    )
                                                if "genre" in result_data:
                                                    st.markdown(
                                                        f"**📚 Genre:** {result_data['genre']}"
                                                    )

                                            with result_col2:
                                                if "target_audience" in result_data:
                                                    st.markdown(
                                                        f"**🎯 Target:** {result_data['target_audience']}"
                                                    )
                                                if "pages_count" in result_data:
                                                    st.markdown(
                                                        f"**📄 Pages:** {result_data['pages_count']}"
                                                    )
                                                if "word_count" in result_data:
                                                    st.markdown(
                                                        f"**📝 Words:** {result_data['word_count']}"
                                                    )

                                            if project.get("word_document_url"):
                                                try:
                                                    # Fetch the Word document from the URL
                                                    import requests

                                                    response = requests.get(
                                                        project["word_document_url"]
                                                    )
                                                    if response.status_code == 200:
                                                        st.download_button(
                                                            "📥 Scarica il file Word",
                                                            data=response.content,
                                                            file_name=f"{project_name.replace(' ', '_')}_final.docx",
                                                            mime="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                                                            key=f"download_word_{project['id']}",
                                                        )
                                                    else:
                                                        st.error(
                                                            f"NOn sono riuscito a scaricare il file Word: {response.status_code}"
                                                        )
                                                except Exception as e:
                                                    st.error(
                                                        f"Error downloading document: {str(e)}"
                                                    )

                                            if project.get("cover_image_url"):
                                                st.markdown(
                                                    f"**🎨 Cover Design:** [View Cover]({project['cover_image_url']})"
                                                )

                                        except (json.JSONDecodeError, KeyError):
                                            st.info(
                                                "Final results data available but format needs updating."
                                            )

                        # Close the project card wrapper
                        st.markdown("</div>", unsafe_allow_html=True)

    else:
        st.info(f"Nessun progetto trovato con stato '{status_filter}'.")

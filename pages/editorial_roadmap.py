import streamlit as st
from utils.auth import get_auth_manager
from utils.config import config
from openai import OpenAI
import json
import uuid
from utils.credits import get_user_id, get_user_credits

# Require authentication
get_auth_manager().require_auth()



# Vision builder is now optional - check if user has one
user_has_vision = get_auth_manager().check_vision_exists(get_user_id())

# Title
st.markdown(
    """
    <h1 style='text-align: center; color: #222; font-size: 3em;'>📚 Editorial Roadmap</h1>
    <h3 style='text-align: center; color: #444;'>Crea la tua serie editoriale logica e coerente</h3>
    """,
    unsafe_allow_html=True,
)

# Initialize session state
if "roadmap_step" not in st.session_state:
    st.session_state["roadmap_step"] = 1
if "book_topics" not in st.session_state:
    st.session_state["book_topics"] = []
if "generated_roadmap" not in st.session_state:
    st.session_state["generated_roadmap"] = None
if "project_name" not in st.session_state:
    st.session_state["project_name"] = ""
if "new_topic_input" not in st.session_state:
    st.session_state["new_topic_input"] = ""

# Progress bar function to avoid module-level session state access
def show_progress():
    # 3 steps total
    current_step = st.session_state.get("roadmap_step", 1)
    progress = min(current_step / 3, 1.0)
    st.progress(progress, text=f"Step {current_step} of 3 - {int(progress * 100)}% Complete")

# Display progress
show_progress()

def generate_project_name(vision_data, topics):
    """Generate a project name based on vision and topics"""
    try:
        client = OpenAI(api_key=config.OPENAI_API_KEY)

        vision_text = vision_data.get('vision', '') if vision_data else ''
        topics_text = ', '.join(topics) if topics else ''

        # Adjust prompt based on whether vision exists
        if vision_text:
            prompt = f"""Based on this author's vision and book topics, generate a concise, professional project name (max 4 words):

Vision: {vision_text}
Topics: {topics_text}

Generate a project name that captures the essence of this editorial series. Use Italian language.
Return only the project name, nothing else."""
        else:
            prompt = f"""Based on these book topics, generate a concise, professional project name (max 4 words):

Topics: {topics_text}

Generate a project name that captures the essence of this editorial series. Use Italian language.
Return only the project name, nothing else."""

        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": "You are an expert editor who creates compelling project names."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.7,

        )

        content = response.choices[0].message.content
        return content.strip() #type: ignore
    except Exception as e:
        st.error(f"Error generating project name: {str(e)}")
        return "Progetto Editoriale"

def generate_editorial_roadmap(vision_data, topics):
    """Generate editorial roadmap with AI"""
    try:
        client = OpenAI(api_key=config.OPENAI_API_KEY)

        vision_text = vision_data.get('vision', '') if vision_data else ''
        topics_list = '\n'.join([f"- {topic}" for topic in topics])

        # Adjust prompt based on whether vision exists
        if vision_text:
            prompt = f"""You are an expert editorial strategist. Based on this author's vision and desired book topics, create a logical editorial roadmap.

AUTHOR VISION:
{vision_text}

DESIRED BOOK TOPICS:
{topics_list}

Create an editorial roadmap that:
1. Orders the topics in the most logical progression for readers
2. Identifies which books should be written first vs. later
3. Shows how each book builds on previous ones
4. Suggests the ideal reading/publication order
5. Identifies any missing topics that would complete the series
IMPORTANT: Use ONLY the exact topics provided. Do not modify or add topics."""
        else:
            prompt = f"""You are an expert editorial strategist. Based on these book topics, create a logical editorial roadmap.

DESIRED BOOK TOPICS:
{topics_list}

Create an editorial roadmap that:
1. Orders the topics in the most logical progression for readers
2. Identifies which books should be written first vs. later
3. Shows how each book builds on previous ones
4. Suggests the ideal reading/publication order

Format your response as a JSON object with this structure:
{{
    "recommended_order": [
        {{
            "position": 1,
            "topic": "exact topic from list",
            "rationale": "why this book should be first",
            "builds_foundation_for": ["topics it prepares readers for"]
        }},
        ...
    ],
    "series_strategy": "overall strategy for the series",
    "cross_referencing_opportunities": ["ways books can reference each other"]
}}

IMPORTANT: Use ONLY the exact topics provided. Do not modify or add topics."""

        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": "You are an expert editorial strategist who creates logical book series progressions."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.7,

        )

        content = response.choices[0].message.content
        try:
            parsed_data = json.loads(content) if content else None
            return parsed_data
        except json.JSONDecodeError:
            # If JSON parsing fails, try to extract content manually
            if content and "recommended_order" in content:
                # Try to clean and parse the content
                import re
                # Extract just the JSON part if there's extra text
                json_match = re.search(r'\{.*\}', content, re.DOTALL)
                if json_match:
                    try:
                        return json.loads(json_match.group())
                    except:
                        pass
            # Last resort fallback
            return {
                "recommended_order": [{"position": i+1, "topic": topic, "rationale": "Ordine suggerito", "builds_on": "N/A", "target_audience_level": "beginner"} for i, topic in enumerate(topics)],
                "missing_topics": [],
                "series_strategy": content or "Strategia non disponibile"
            }
    except Exception as e:
        st.error(f"Error generating roadmap: {str(e)}")
        return None

# Step 1: Input book topics
if st.session_state.get("roadmap_step", 1) == 1:
    st.markdown("### 📝 Inserisci i Tuoi Argomenti")
    st.markdown("Elenca tutti gli argomenti/libri che vorresti scrivere nella tua serie editoriale:")

    # Form for adding topics
    with st.form("topics_form"):
        new_topic = st.text_input(
            "Aggiungi un argomento/titolo di libro:",
            value=st.session_state["new_topic_input"],
            placeholder="Es: Come iniziare con le erbe medicinali"
        )
        add_topic = st.form_submit_button("➕ Aggiungi Argomento")

    if add_topic and new_topic.strip(): #type: ignore
        if new_topic.strip() not in st.session_state["book_topics"]: #type: ignore
            st.session_state["book_topics"].append(new_topic.strip()) #type: ignore
            st.session_state["new_topic_input"] = ""  # Reset the input field
            st.rerun()
        else:
            st.warning("Questo argomento è già presente nella lista.")

    # Display current topics
    if st.session_state["book_topics"]:
        st.markdown("### 📋 Argomenti Inseriti:")
        for i, topic in enumerate(st.session_state["book_topics"]):
            col1, col2 = st.columns([4, 1])
            with col1:
                st.markdown(f"**{i+1}.** {topic}")
            with col2:
                if st.button("🗑️", key=f"delete_{i}", help="Rimuovi argomento"):
                    st.session_state["book_topics"].pop(i)
                    st.rerun()

    # Continue button
    if len(st.session_state["book_topics"]) >= 1:
        if st.button("🎯 Genera Roadmap Editoriale", type="primary"):
            st.session_state["roadmap_step"] = 2
            st.rerun()
    else:
        st.info("💡 Inserisci almeno 1 argomento per generare la roadmap editoriale.")

# Step 2: Generate roadmap
elif st.session_state.get("roadmap_step", 1) == 2:
    st.markdown("### 🎯 Generazione Roadmap in Corso...")

    with st.spinner("Analizzando la tua vision e creando la roadmap ottimale..."):
        # Get user vision if exists
        vision_data = get_auth_manager().get_user_vision(get_user_id()) if user_has_vision else None

        # Generate roadmap
        roadmap = generate_editorial_roadmap(vision_data, st.session_state["book_topics"])
        st.session_state["generated_roadmap"] = roadmap

        # Generate project name
        project_name = generate_project_name(vision_data, st.session_state["book_topics"])
        st.session_state["project_name"] = project_name

        if roadmap:
            st.success("✅ Roadmap editoriale generata!")
            st.session_state["roadmap_step"] = 3
            st.rerun()
        else:
            st.error("❌ Errore nella generazione della roadmap. Riprova.")

# Step 3: Display and save roadmap
elif st.session_state.get("roadmap_step", 1) == 3:
    roadmap = st.session_state["generated_roadmap"]
    project_name = st.session_state["project_name"]

    if roadmap:
        st.markdown(f"### 🎯 La Tua Roadmap Editoriale: **{project_name}**")
        # Display strategy
        if roadmap.get("series_strategy"):

            strategy_text = roadmap["series_strategy"]

            # Clean the strategy text - it should already be properly extracted from JSON
            if isinstance(strategy_text, str) and strategy_text.strip():
                st.markdown(strategy_text)
            else:
                st.info("Strategia editoriale non disponibile")
        st.markdown("---")
        # Display recommended order
        st.markdown("#### 📚 Ordine Consigliato di Pubblicazione:")

        books_order = roadmap.get("recommended_order", [])
        for i, book in enumerate(books_order):
            # Create a card-like layout for each book
            level_color = {"beginner": "🟢", "intermediate": "🟡", "advanced": "🔴"}
            level_icon = level_color.get(book.get('target_audience_level', 'beginner'), "⚪")

            # Create a clean card layout
            if i == 0:
                st.success(f"**🚀 Libro #{book.get('position', i+1)}: {book.get('topic', 'Titolo non disponibile')}** (Inizia da qui!)")
            elif i == 1:
                st.info(f"**📖 Libro #{book.get('position', i+1)}: {book.get('topic', 'Titolo non disponibile')}**")
            else:
                st.markdown(f"**📖 Libro #{book.get('position', i+1)}: {book.get('topic', 'Titolo non disponibile')}**")


            st.write(f"**💡 Perché questo ordine:** {book.get('rationale', 'Motivazione non disponibile')}")
            st.write(f"**🔗 Si basa su:** {book.get('builds_on')}" if book.get('builds_on') else "")

            st.write(f"**🎯 Livello:** {level_icon} {book.get('target_audience_level', 'beginner').title()}")

            st.markdown("---")

        # Display missing topics
        if roadmap.get("missing_topics"):
            st.markdown("#### 💡 Argomenti Suggeriti per Completare la Serie:")
            for topic in roadmap["missing_topics"]:
                st.markdown(f"• {topic}")


        # Save project button
        col1, col2 = st.columns(2)
        with col1:
            if st.button("💾 Salva Progetto e Inizia", type="primary", use_container_width=True):
                try:
                    # Prepare project data
                    project_data = {
                        "project_type": "editorial_series",
                        "vision_reference": get_auth_manager().get_user_vision(get_user_id()) if user_has_vision else None,
                        "editorial_roadmap": roadmap,
                        "book_topics": st.session_state["book_topics"],
                        "current_book_index": 0,
                        "books_status": [{"topic": book["topic"], "status": "planned", "completed_steps": []} for book in roadmap.get("recommended_order", [])],
                        "step_data": {},
                        "metadata": {
                            "created_from": "editorial_roadmap",
                            "total_books_planned": len(roadmap.get("recommended_order", [])),
                            "series_strategy": roadmap.get("series_strategy", "")
                        }
                    }

                    # Save project to database
                    success = get_auth_manager().save_project(get_user_id(), project_name, project_data)

                    if success:
                        st.success("✅ Progetto salvato! Iniziamo con il primo libro.")
                        st.balloons()

                        # Set the first book topic for step1
                        if roadmap.get("recommended_order"):
                            first_book = roadmap["recommended_order"][0]
                            st.session_state["current_project_name"] = project_name
                            st.session_state["current_book_topic"] = first_book["topic"]
                            st.session_state["project_roadmap"] = roadmap

                        # Redirect to projects dashboard
                        st.switch_page("pages/dashboard/projects.py")
                    else:
                        st.error("❌ Errore nel salvataggio del progetto.")
                except Exception as e:
                    st.error(f"❌ Errore: {str(e)}")

        with col2:
            if st.button("🔄 Rigenera Roadmap", use_container_width=True):
                st.session_state["roadmap_step"] = 1
                st.session_state["generated_roadmap"] = None
                st.rerun()

# Footer
st.markdown("---")
st.markdown(
    f"""
    <div style="text-align: center; color: #666; font-size: 0.8em;">
        <p>Crea una strategia editoriale vincente con KDP GENIUS!</p>
    </div>
    """,
    unsafe_allow_html=True,
)

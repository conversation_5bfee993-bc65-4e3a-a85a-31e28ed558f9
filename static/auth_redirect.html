<!doctype html>
<html>
    <head>
        <title>Authentication - KDP GENIUS</title>
        <meta charset="utf-8" />
        <style>
            body {
                font-family:
                    -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
                    sans-serif;
                display: flex;
                justify-content: center;
                align-items: center;
                height: 100vh;
                margin: 0;
                background-color: #f0f2f6;
            }
            .container {
                text-align: center;
                padding: 2rem;
                background: white;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
                max-width: 400px;
            }
            .spinner {
                border: 3px solid #f3f3f3;
                border-top: 3px solid #1e3a8a;
                border-radius: 50%;
                width: 40px;
                height: 40px;
                animation: spin 1s linear infinite;
                margin: 0 auto 1rem;
            }
            @keyframes spin {
                0% {
                    transform: rotate(0deg);
                }
                100% {
                    transform: rotate(360deg);
                }
            }
            .error {
                color: #dc2626;
                margin-top: 1rem;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="spinner"></div>
            <h2>Processing Authentication...</h2>
            <p>Please wait while we redirect you.</p>
            <p id="error-message" class="error" style="display: none"></p>
        </div>

        <script>
            // Get the hash from the URL
            const hash = window.location.hash.substring(1);

            if (hash) {
                // Build the URL with QUERY parameters, not hash
                const redirectUrl = "https://app.kdpgenius.com?" + hash;

                // Force redirect - use href assignment for proper redirect
                window.location.href = redirectUrl;
            } else {
                // No hash parameters - show error and redirect
                document.getElementById("error-message").style.display =
                    "block";
                document.getElementById("error-message").textContent =
                    "No authentication parameters found.";

                setTimeout(() => {
                    window.location.href = "https://app.kdpgenius.com/";
                }, 3000);
            }
        </script>
    </body>
</html>

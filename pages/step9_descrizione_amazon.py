from utils.auth import get_auth_manager
import streamlit as st
from utils.credits import get_user_id, get_user_credits
from components.help_chat import show_help_chat
# Require authentication
get_auth_manager().require_auth()


import os
import streamlit as st
from agents.descrizione_agent import genera_descrizione
from utils.config import config



st.markdown(
    """
<div style="background-color:#f0f8ff; padding:25px; border-radius:10px; margin-bottom:20px;">
    <h1 style="color:#0d47a1; text-align:center;">🛍️ Step 9: Descrizione Amazon</h1>
    <p style="text-align:center;">Scrivi una descrizione potente, ispirata ai modelli di successo.</p>
</div>
""",
    unsafe_allow_html=True,
)

# Get language from global setting
LINGUE = {
    "it": "italiano",
    "en": "inglese",
    "es": "spagnolo",
    "fr": "francese",
    "de": "tedesco",
}

# Progress bar function
def show_progress():
    """Display progress bar"""
    st.progress(min(9 / 12, 1.0), text=f"{int(9 / 12 * 100)}% completato")

show_progress()

# Display language info
st.info(f"🛍️ Lingua della descrizione: {LINGUE.get(st.session_state.get('lingua_target', 'it'), 'italiano').capitalize()}")

# Display idea analysis warnings if relevant
if st.session_state.get("idea_analysis"):
    analysis = st.session_state["idea_analysis"]

    if analysis.get("critical_issues") or analysis.get("content_disclaimers"):
        st.markdown("### ⚠️ Considerazioni dall'Analisi del Concept")

        if analysis.get("critical_issues"):
            with st.expander("🚨 Problematiche da Considerare nella Descrizione"):
                for issue in analysis["critical_issues"]:
                    st.error(f"❌ {issue}")

        if analysis.get("content_disclaimers"):
            with st.expander("📋 Disclaimer da Considerare"):
                for disclaimer in analysis["content_disclaimers"]:
                    st.info(f"📋 {disclaimer}")

# Tone selector will be in main content area
st.markdown("### 📝 Impostazioni Descrizione")

# Selettore tono
tono = st.selectbox(
    "Tono della descrizione",
    ["Coinvolgente", "Professionale", "Empatico", "Ispirazionale", "Narrativo"],
    key="tono_descrizione"
)

# Prerequisiti
def validate_prerequisites():
    """Check prerequisites only if moving forward"""
    required_keys = [
        "argomento_keyword",
        "buyer_persona_generata",
        "posizionamento_editoriale",
        "indice_libro_generato",
        "idea_analysis",
    ]
    last_completed = st.session_state.get("last_step_completed", 0)

    # Only validate if we're moving forward (not backward navigation)
    if last_completed < 9:  # Step 9 is the current step
        if not all(k in st.session_state for k in required_keys):
            st.warning("⚠️ Completa prima gli step precedenti.")
            st.stop()

# Run validation
validate_prerequisites()


# # Prompt guida
# with st.expander("💡 Prompt AI consigliato per la descrizione Amazon"):
#     st.code(
#         """
#     Struttura:
#     1. Titolo promozionale
#     2. Domande coinvolgenti con parole chiave
#     3. Paragrafo introduttivo sul problema
#     4. Sezione "The problem"
#     5. Sezione "But what if..."
#     6. "This book is your complete guide to..."
#     7. Lista bullet "What you'll find inside"
#     8. Paragrafo motivazionale
#     9. Call to Action finale
#     Formatta con Markdown, scrivi nella lingua scelta, senza preamboli generici.
#     """,
#         language="markdown",
#     )

if st.button("📝 Genera Descrizione Amazon (5 crediti)", type="primary"):
    # Check credits before generation
    if get_user_credits() < 5:
        st.error("❌ Crediti insufficienti! La generazione della descrizione Amazon richiede 5 crediti.")
        st.info("💳 Puoi acquistare più crediti dalla dashboard.")
        st.stop()

    with st.spinner("Generazione in corso..."):
        try:
            dati = {
                "Argomento": st.session_state["argomento_keyword"],
                "Buyer Persona": st.session_state["buyer_persona_generata"],
                "Posizionamento": st.session_state["posizionamento_editoriale"],
                "Indice": st.session_state["indice_libro_generato"],
                "Pain Points": st.session_state.get("pain_points", ""),
                "Jobs To Be Done": st.session_state.get("jobs_to_be_done", ""),
                "Idea Analysis": st.session_state.get("idea_analysis", {}),
            }

            # Get idea analysis context
            idea_analysis = dati.get("Idea Analysis", {})
            analysis_context = ""
            if idea_analysis:
                critical_issues = idea_analysis.get("critical_issues", [])
                disclaimers = idea_analysis.get("content_disclaimers", [])
                recommendations = idea_analysis.get("recommendations", [])

                if critical_issues or disclaimers or recommendations:
                    analysis_context = f"""

            ANALISI RISCHI E COMPLIANCE (Step 7):
            - Problematiche critiche: {'; '.join(critical_issues) if critical_issues else 'Nessuna'}
            - Disclaimer necessari: {'; '.join(disclaimers) if disclaimers else 'Nessuno'}
            - Livello rischio: {idea_analysis.get('overall_risk_level', 'Non specificato')}
            - Raccomandazioni marketing: {'; '.join([r for r in recommendations if any(keyword in r.lower() for keyword in ['marketing', 'descrizione', 'vendita', 'pubblico'])]) if recommendations else 'Nessuna'}
            """

            prompt = f"""
            Sei un il miglior copywriter professionista multilingue al mondo estremamente specializato nella creazione di descrizioni Amazon KDP. Il tuo compito è scrivere una descrizione Amazon in lingua {LINGUE.get(st.session_state['lingua_target'])} che segua fedelmente questo schema, usato per libri non-fiction in stile promozionale americano.
            La descrizione deve essere estremamente persuasiva e coinvolgente, utilizzando parole chiave e frasi chiave per attirare l'attenzione del lettore e promuovere l'acquisto del libro.

            📚 Argomento del libro: {dati.get("Argomento", "N/D")}
            👤 Buyer Persona: {dati.get("Buyer Persona", "")}
            🎯 Posizionamento editoriale: {dati.get("Posizionamento", "")}
            🧠 Pain Points principali: {dati.get("Pain Points", "")}
            📖 Indice o contenuti principali: {dati.get("Indice", "")}
            📌 Lingua richiesta: {LINGUE.get(st.session_state['lingua_target'])}
            {analysis_context}

            ✍️ Struttura da replicare:
            #DESCRIPTION TITLE#
            Descrizione: La descrizione deve essere formattata correttamente, facendo in modo che i 4 blocchi siano ben visibili, poi staccando bene i blocchi con degli spazi per rendere “comoda” la lettura. Non fate mai “muri di testo”, quindi frasi lunghe 6-7 righe insieme. Spezzate i vari blocchi per renderli più scorrevoli. Mettete in grassetto le parole chiave o le frasi d'impatto; potete anche sottolineare i concetti chiave. La descrizione dovrebbe essere composta da 4 blocchi principali. NUMERO MASSIMO DI PAROLE CIRCA 250-300 usa le parole chiavi più pertinenti .
            #BLOCCO 1: TESTATA - PROBLEMI DEL PUBBLICO DESTINATARIO
            Creare una TESTATA (una frase forte e positiva che colpisca il vero obiettivo del pubblico destinatario e catturi la sua attenzione) Continuare con 2 domande relative ai problemi principali del pubblico destinatario. L'obiettivo di queste domande è far sì che il cliente risponda “SI”. Pertanto, domande estremamente precise che mirano direttamente al problema. Utilizzate il formato “titolo 4” e “grassetto” nella formattazione di KDP per far apparire le domande in un carattere più grande nell'anteprima.

            #BLOCCO 2: INDICAZIONE DEI PUNTI DOLENTI + EMPATIA E PRESENTAZIONE DELLA SOLUZIONE
            in questa parte della descrizione è necessario enfatizzare i punti dolenti del target, quindi bisogna andare ad amplificare il problema per far sentire al cliente un “senso di urgenza” ancora maggiore di quello che ha già. Allo stesso tempo, dopo aver amplificato il problema, entriamo in empatia con il cliente, facendogli capire che sappiamo come si sente e rassicurandolo con la soluzione (KW O ARGOMENTO)

            #BLOCCO 3: PUNTI BULLET ATTRATTIVI
            Punti elenco, quindi soluzioni ai problemi riscontrati in precedenza. A seconda delle domande scelte, utilizzate gli argomenti giusti che vanno a soddisfare le esigenze. Strutturate i bullet point in questo modo, quindi utilizzate un nome (accattivante) per il bullet point, in grassetto, e poi seguite con una breve spiegazione del contenuto evidenziandone i vantaggi. Massimo 6-7 punti elenco. piu’il bonus

            #BLOCCO 4: SOGNO+CTA
            # in questa fase, è necessario “far sognare il cliente con il suo obiettivo finale”. Dopo la prima parte in cui si fa sognare il cliente e si capisce quali obiettivi potrebbe raggiungere, lo si invita all'acquisto con una “call to action” coinvolgente e stimolante.

            📌 Requisiti:
            - La descrizione deve essere ASSOLUTAMENTE in {LINGUE.get(st.session_state['lingua_target'])}
            - Usa Markdown con **grassetto** per titoli e frasi chiave
            - Non aggiungere contenuti generici
            - Segui fedelmente la struttura.
            - IMPORTANTE: Evita contenuti che potrebbero violare le politiche Amazon KDP
            - Se ci sono disclaimer necessari, includili appropriatamente nella descrizione
            - Adatta il linguaggio per minimizzare i rischi identificati nell'analisi
            - IMPORTANTE - DEVE SEGUIRE LA STRUTTURA PRESENTATA E NON RACCONTARE UNA STORIA ALLA BUYER PERSONA.
            - IMPORTANTE - non inventarti contenuti che non sono stati definiti nell’indice e/o nella pianificazione editoriale
            """

            descrizione = genera_descrizione(
                dati,
                tono,
                config.OPENAI_API_KEY,
                prompt,
            )
            st.session_state["descrizione_amazon"] = descrizione

            # Deduct credits for successful generation
            user_id = get_user_id()
            get_auth_manager().deduct_credits(user_id, "content_generation", 5, description="Amazon description generation")

            # Save step 9 data
            current_project_id = st.session_state.get("current_project_id")
            if current_project_id:
                step_data = {
                    "descrizione_amazon": descrizione
                }
                user_id = get_user_id()
                get_auth_manager().save_step_data(user_id, current_project_id, 9, step_data)

            st.success("✅ Descrizione generata correttamente!")
        except Exception as e:
            st.error(f"❌ Errore durante la generazione AI: {str(e)}")

if "descrizione_amazon" in st.session_state:
    st.markdown("---")
    st.markdown("### 📝 Descrizione Generata:")
    st.markdown(st.session_state["descrizione_amazon"], unsafe_allow_html=True)

    # Add regenerate option
    if st.button("🔄 Rigenera Descrizione (5 crediti)", use_container_width=True):
        st.session_state.pop("descrizione_amazon", None)
        st.rerun()

    st.markdown("---")
    col1, col2 = st.columns(2)

    with col1:
        if st.button("⬅️ Indietro", use_container_width=True):
            st.session_state["current_step"] = 8
            st.switch_page("pages/step8_indice_perfetto.py")

    with col2:
        if st.button("✅ Conferma e continua", type="primary", use_container_width=True):
            # Ensure data is saved before proceeding
            current_project_id = st.session_state.get("current_project_id")
            if current_project_id and not st.session_state.get("step9_saved"):
                step_data = {
                    "descrizione_amazon": st.session_state.get("descrizione_amazon", "")
                }
                user_id = get_user_id()
                result = get_auth_manager().save_step_data(user_id, current_project_id, 9, step_data)
                if result.get("success"):
                    st.session_state["step9_saved"] = True

            st.session_state["current_step"] = 10
            st.switch_page("pages/step10_cover_perfetta.py")

# Show help chat for step9
show_help_chat("step9_descrizione_amazon")

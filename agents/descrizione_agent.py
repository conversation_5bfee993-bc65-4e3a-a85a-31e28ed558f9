import openai


def genera_descrizione(dati, tono, openai_key, prompt_custom, lingua="italiano"):
    if not openai_key:
        raise ValueError("❌ Chiave OpenAI mancante.")
    try:
        client = openai.OpenAI(api_key=openai_key)
        prompt = f"""{prompt_custom}

📚 Argomento: {dati.get('Argomento', 'N/D')}
👤 Buyer Persona: {dati.get('Buyer Persona', 'N/D')}
🧭 Posizionamento: {dati.get('Posizionamento', 'N/D')}
📖 Indice: {dati.get('Indice', 'N/D')}
🎯 Tono: {tono}
Scrivi in {lingua}.
"""
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {
                    "role": "system",
                    "content": f"Sei il miglior copywriter editoriale e markter al mondo esperto di publicazioni KDP al mondo in lingua {lingua}.",
                },
                {"role": "user", "content": prompt},
            ],

        )
        return response.choices[0].message.content
    except Exception as e:
        raise RuntimeError(f"Errore nella generazione della descrizione: {e}")

import openai


def genera_posizionamento(dati, openai_key, prompt_custom, lingua="italiano"):
    if not openai_key:
        raise ValueError("❌ Chiave OpenAI mancante.")

    prompt = f"""
{prompt_custom}

📈 Analisi Keyword: {dati.get('Analisi Keyword', 'N/D')}
👤 Buyer Persona: {dati.get('Buyer Persona', 'N/D')}
😠 Problemi Recensioni: {dati.get('Problemi Recensioni', 'N/D')}

📌 Scrivi in {lingua}.
"""
    try:
        client = openai.OpenAI(api_key=openai_key)
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {
                    "role": "system",
                    "content": f"Sei un esperto KDP che scrive in {lingua}.",
                },
                {"role": "user", "content": prompt},
            ],
            temperature=0.7,
            max_tokens=1600,
        )
        return response.choices[0].message.content
    except Exception as e:
        raise RuntimeError(f"❌ Errore nella generazione del posizionamento: {e}")

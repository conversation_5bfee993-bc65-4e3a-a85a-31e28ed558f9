import streamlit as st
from functools import wraps
from utils.auth import get_auth_manager


def get_user_id():
    """Get user data from session state"""
    user_data = st.session_state.get("user_data", {})
    user_id = user_data.get("id")

    if not user_id:
        st.error("User ID not found. Please login again.")
        st.switch_page("pages/auth_login.py")
        st.stop()

    return user_id

def get_user_credits():
    """Get current user credits"""
    user_id = get_user_id()
    user_profile = get_auth_manager().get_user_profile(user_id)
    return user_profile.get("credits", 0) if user_profile else 0

def require_credits(credits_needed=1, action_type="other", description=None):
    """
    Decorator to check if user has sufficient credits before executing a function

    Args:
        credits_needed (int): Number of credits required
        action_type (str): Type of action for tracking
        description (str): Description of the action
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Get user data
            user_data = st.session_state.get("user_data", {})
            user_id = user_data.get("id")

            if not user_id:
                st.error("Please login to continue")
                return None

            # Check if user has sufficient credits
            if not get_auth_manager().check_sufficient_credits(user_id, credits_needed):
                st.error(f"Insufficient credits! You need {credits_needed} credits for this action.")
                st.info("Purchase more credits in the Billing section.")
                if st.button("💰 Go to Billing"):
                    st.switch_page("pages/dashboard/billing.py")
                return None

            # Execute the function
            result = func(*args, **kwargs)

            # If function executed successfully, deduct credits
            if result is not None:
                success = get_auth_manager().deduct_credits(
                    user_id=user_id,
                    amount=credits_needed,
                    action_type=action_type,
                    description=description #type: ignore
                )

                if success:
                    # Update session state credits
                    current_credits = user_data.get("credits", 0)
                    st.session_state["user_data"]["credits"] = current_credits - credits_needed

                    # Show success message
                    st.success(f"✅ Action completed! {credits_needed} credit(s) used.")
                else:
                    st.error("Failed to deduct credits. Please try again.")

            return result
        return wrapper
    return decorator

def check_credits_before_action(user_id, credits_needed=1):
    """
    Simple function to check credits before an action

    Returns:
        bool: True if user has sufficient credits, False otherwise
    """
    if not user_id:
        return False

    return get_auth_manager().check_sufficient_credits(user_id, credits_needed)

def deduct_credits_for_action(user_id, credits_needed=1, action_type="other", description=None, project_id=None):
    """
    Deduct credits for a specific action

    Returns:
        bool: True if credits were deducted successfully, False otherwise
    """
    if not user_id:
        return False

    success = get_auth_manager().deduct_credits(
        user_id=user_id,
        amount=credits_needed,
        action_type=action_type,
        description=description, #type: ignore
        project_id=project_id #type: ignore
    )

    if success:
        # Update session state if available
        user_data = st.session_state.get("user_data", {})
        if user_data.get("id") == user_id:
            current_credits = user_data.get("credits", 0)
            st.session_state["user_data"]["credits"] = max(0, current_credits - credits_needed)

    return success

def display_credit_warning(credits_needed=1):
    """Display a warning about credit usage"""
    user_data = st.session_state.get("user_data", {})
    current_credits = user_data.get("credits", 0)

    if current_credits < credits_needed:
        st.error(f"⚠️ You need {credits_needed} credits to perform this action. You currently have {current_credits} credits.")
        return False
    elif current_credits <= 10:
        st.warning(f"⚠️ Low credits! You have {current_credits} credits remaining. Consider purchasing more.")

    return True

def get_credits_info():
    """Get current user credits info"""
    user_data = st.session_state.get("user_data", {})
    return {
        "current_credits": user_data.get("credits", 0),
        "user_id": user_data.get("id"),
        "plan": user_data.get("subscription_status", "free")
    }

def display_credits_sidebar():
    """Display credits info in sidebar"""
    user_data = st.session_state.get("user_data", {})
    credits = user_data.get("credits", 0)

    if credits > 50:
        credit_color = "#28a745"
    elif credits > 10:
        credit_color = "#ffc107"
    else:
        credit_color = "#dc3545"

    st.sidebar.markdown(
        f"💳 <span style='color: {credit_color};'>{credits} credits</span>",
        unsafe_allow_html=True
    )

    if credits <= 5:
        st.sidebar.error("⚠️ Low credits!")
        if st.sidebar.button("💰 Buy Credits"):
            st.switch_page("pages/dashboard/billing.py")

# Credit costs for different actions
CREDIT_COSTS = {
    "keyword_analysis": 1,
    "review_analysis": 2,
    "content_generation": 3,
    "cover_generation": 2,
    "title_generation": 1,
    "description_generation": 2,
    "buyer_persona": 2,
    "positioning": 1,
    "index_generation": 3,
    "other": 1
}

def get_action_cost(action_type):
    """Get credit cost for a specific action"""
    return CREDIT_COSTS.get(action_type, 1)

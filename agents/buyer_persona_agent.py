import openai


def genera_buyer_persona(dati, fonti_web, openai_key, prompt_custom):

    if not openai_key:
        raise ValueError("❌ Chiave OpenAI mancante. Inseriscila nella Home.")
    try:
        client = openai.OpenAI(api_key=openai_key)
        messages = [
            {
                "role": "system",
                "content": f"Sei un esperto di marketing editoriale e copywriting per Amazon KDP. Scri vi in modo professionale e dettagliato, creando una buyer persona per un libro basato su questi dati:\n\n{dati}\n\nFonti web: {fonti_web}",
            },
            {"role": "user", "content": prompt_custom},
        ]
        response = client.chat.completions.create(
            model="o3", messages=messages,
        )
        content = response.choices[0].message.content
        return content.strip() if content else ""
    except Exception as e:
        raise RuntimeError(f"Errore durante la generazione della buyer persona: {e}")

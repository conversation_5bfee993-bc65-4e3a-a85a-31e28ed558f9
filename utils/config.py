import os
from dotenv import load_dotenv
import streamlit as st

class Config:
    def __init__(self):
        load_dotenv(dotenv_path=".env")

    @property
    def OPENAI_API_KEY(self):
        return os.getenv("OPENAI_API_KEY", "")

    @property
    def SERPAPI_API_KEY(self):
        return os.getenv("SERPAPI_API_KEY", "")

    @property
    def PERPLEXITY_API_KEY(self):
        return os.getenv("PERPLEXITY_API_KEY", "")

    @property
    def AXESSO_KEY(self):
        return os.getenv("AXESSO_KEY", "")

    @property
    def SUPABASE_URL(self):
        return os.getenv("SUPABASE_URL", "")

    @property
    def SUPABASE_KEY(self):
        return os.getenv("SUPABASE_KEY", "")

    @property
    def STRIPE_SECRET_KEY(self):
        return os.getenv("STRIPE_SECRET_KEY", "")

    @property
    def STRIPE_PUBLISHABLE_KEY(self):
        return os.getenv("STRIPE_PUBLISHABLE_KEY", "")

    @property
    def STRIPE_WEBHOOK_SECRET(self):
        return os.getenv("STRIPE_WEBHOOK_SECRET", "")

    @property
    def SUPABASE_SERVICE_KEY(self):
        return os.getenv("SUPABASE_SERVICE_KEY", "")
    @property
    def app_url(self):
        return os.getenv("app_url", "")

    @property
    def KEEPA_KEY(self):
        return os.getenv("KEEPA_KEY", "")




# Global instance
config = Config()

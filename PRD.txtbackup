# KDP STRATEGA - Product Requirements Document (PRD)

## Project Overview
KDP Stratega is an AI-powered publishing platform for Amazon KDP authors. The platform helps authors research, plan, and create successful Kindle Direct Publishing books through AI-driven analysis and content generation.

## Technical Stack
- Frontend: Streamlit
- Database: Supabase (PostgreSQL)
- Authentication: Supabase Auth
- Payments: Stripe Checkout
- AI Services: OpenAI, Perplexity, SerpAPI

## 1. AUTHENTICATION SYSTEM ✅ COMPLETED

### 1.1 User Authentication
- **Implementation**: Using st_supabase_connection for Supabase Auth integration
- **Pages**: Login, Sign Up, Forgot Password
- **Security**: Row Level Security (RLS) policies, secure password hashing
- **Session Management**: Streamlit session state with automatic logout

### 1.2 User Onboarding - Vision Builder ✅ COMPLETED
**Process**: 3-step onboarding flow for new users

**Phase 1 - AI Question Generation**:
The AI produces exactly three open-ended questions rich in psychological insights to discover:
- The author's fundamental values and motivations
- Their personal identity as a creator
- Their final goals in terms of impact and audience

**Phase 2 - Vision Synthesis**:
Template for AI processing:
```
Phase 1 Questions:
1. [First psychological question]
2. [Second psychological question]
3. [Third psychological question]

ANSWERS:
1. [Author's reply to question 1]
2. [Author's reply to question 2]
3. [Author's reply to question 3]

INSTRUCTIONS:
Generate an "Author Vision" statement that:
• Identifies who this author is (values, motivations, identity)
• Describes their editorial tone (voice, style, core values)
• Summarizes their key goals and the impact they wish to create
Write as a cohesive paragraph (3–5 sentences). Synthesize, don't quote directly.
```

**Storage**: Vision data stored in `vision_builder` table with complete question/answer tracking

## 2. PAYMENT SYSTEM ✅ COMPLETED

### 2.1 Stripe Integration
- **Checkout**: Stripe Checkout Sessions for one-time payments
- **Webhooks**: Automated payment confirmation and credit allocation
- **Security**: Webhook signature validation, secure API key management

### 2.2 Credit System
**Credit Packages**:

- Popular: 50 credits - €17.99
- Pro: 100 credits + 50 bonus - €39.99 Popular
- Business: 200 credits + 100 bonus - €69.99



### 5.1 Multi-Step Process (11 Steps) ✅ COMPLETED
1. **Home** - Project initialization
2. **Keywords** - Amazon keyword research (10 credits)
3. **Reviews** - Competitor review analysis (10 credits)
4. **Buyer Persona** - Target audience definition (8 credits)
5. **Positioning** - Market positioning strategy (8 credits)
6. **Title** - Title and subtitle generation (5 credits)
----
before index, run a step 6b called
"idea validator

Step6b: Idea Analyzer (5 credits)
Analyzes your book idea for originality, relevance to your audience, and potential risks (e.g., banned words or medical claims - check that Checks that your title and subtitle do not violate trademarks, KDP banned terms, or critical policies. Alerts you to risks right away.). Guides you to an informed choice. So you can run previous step and inform step 7 and 8 about potential risk bfore generating the book structure

----
7. **Index** - Book structure creation (8 credits)
8. **Description** - Amazon description writing (5 credits)
9. **Cover** - Cover design suggestions (5 credits + 20 credits)
10. **Summary** - Project overview compilation (1 credit)
11. **Export** - Download Word document



**Features**:
- Automatic credit deduction with usage logging
- Real-time balance checking
- Usage analytics and history
- Low credit warnings

## 3. DATABASE ARCHITECTURE ✅ COMPLETED

### 3.1 Core Tables

**users**:
- id (UUID) - References auth.users
- name, email
- credits (INTEGER)
- stripe_customer_id
- onboarding_completed, vision_completed
- created_at, updated_at

**vision_builder**:
- id (UUID), user_id (UUID FK)
- question_1, answer_1, question_2, answer_2, question_3, answer_3
- vision (TEXT) - Generated vision statement
- created_at, updated_at

**projects**:
- id (SERIAL) - Integer primary key
- user_id (UUID FK)
- project_name, project_data (JSONB)
- status, progress_percentage, final_result
- created_at, updated_at

**transactions**:
- id (UUID), user_id (UUID FK)
- stripe_session_id, stripe_customer_id
- amount, currency, credits_purchased
- transaction_type, status, metadata
- created_at, updated_at

**credits_usage**:
- id (UUID), user_id (UUID FK)
- action_type, credits_used
- project_id (INTEGER FK) - References projects.id
- description, metadata
- created_at

### 3.2 Database Functions
- `deduct_user_credits()` - Atomic credit deduction with usage logging
- Automatic user profile creation trigger
- Updated timestamp triggers

**⚠️ CRITICAL TECHNICAL LIMITATION**:
- **NEVER USE RPC CALLS** with st_supabase_connection
- RPC calls (`self.conn.rpc()`) corrupt the session state and break authentication
- Always use direct table queries (`self.conn.table()`) instead
- Manual data aggregation required instead of database functions

### 3.3 Security
- Row Level Security (RLS) enabled on all tables
- User isolation policies
- Service role permissions for payment operations
- Secure function execution with SECURITY DEFINER

## 4. USER INTERFACE ✅ COMPLETED

### 4.1 Navigation Structure
**Sidebar Navigation** (always visible for authenticated users):
- Dashboard sections: Overview, Profile, Billing, Projects
- Quick actions: New Project, Vision Builder
- Book creation workflow (11 steps)
- Credit display with color coding
- Logout functionality

### 4.2 Dashboard Pages
- **Overview**: Account summary, recent projects, quick actions
- **Profile**: User information management
- **Billing**: Credit packages, transaction history, usage analytics
- **Projects**: Project management and history

### 4.3 Payment Flow
- **Billing Page**: Credit package selection → Stripe Checkout
- **Payment Success**: Confirmation → Credit allocation → Redirect options
- **Error Handling**: Failed payments, insufficient credits, webhook failures

## 5. BOOK CREATION WORKFLOW

### 5.1 Multi-Step Process (11 Steps) ✅ COMPLETED
1. **Home** - Project initialization
2. **Keywords** - Amazon keyword research (10 credits)
3. **Reviews** - Competitor review analysis (10 credits)
4. **Buyer Persona** - Target audience definition (8 credits)
5. **Positioning** - Market positioning strategy (8 credits)
6. **Title** - Title and subtitle generation (5 credits)
7. **Index** - Book structure creation (8 credits)
8. **Description** - Amazon description writing (5 credits)
9. **Cover** - Cover design suggestions (5 credits + 20 credits)
10. **Summary** - Project overview compilation (1 credit)
11. **Export** - Download Word document

**Total per book**: ~85 credits (allowing 1.5 books per 100 credits)

### 5.2 Project Management System ✅ COMPLETED
**Features**:
- **Modular Data Storage**: Projects use JSONB field for step-by-step data
- **Project Opening**: Load any project and continue from last completed step
- **Progress Tracking**: Real-time progress percentage and step completion
- **Status Management**: Draft → In Progress → Completed workflow
- **Session State Sync**: All form fields pre-populated from saved data
- **Delete Functionality**: Complete project removal with confirmation

**Database Schema**:
```json
{
  "step_data": {
    "step1": {"marketplace": "IT", "lingua_target": "it", "completed": true},
    "step2": {"argomento_keyword": "...", "analisi_argomento_generata": "..."},
    // ... other steps
  },
  "current_step": 5,
  "last_step_completed": 4
}
```

### 5.3 Credit Integration ✅ COMPLETED
- Credit check before each AI operation
- Automatic deduction upon successful completion
- Usage tracking by project and action type
- Insufficient credit handling with purchase prompts
- Real-time credit balance display in sidebar
- Smart credit allocation (100 credits = 1.5 books)

## 6. DEVELOPMENT STATUS

### ✅ COMPLETED FEATURES
- Complete authentication system with Supabase
- Vision Builder onboarding flow
- Stripe payment integration (checkout + webhooks)
- Credit system with usage tracking
- Database architecture with RLS
- Dashboard with analytics
- Navigation and UI structure
- Payment success/failure handling
- **Project Management System**: Create, save, load, delete projects
- **Modular Step Data**: JSONB-based step storage with helper methods
- **Project Dashboard**: Card-based layout with progress visualization
- **Session Restoration**: Complete state restoration from database
- **Credit Integration**: Smart deduction across all workflow steps

### 🔄 FINAL TESTING REQUIREMENTS
1. **Database Setup**: ✅ Migration scripts completed and verified
2. **Environment Configuration**: ✅ All API keys configured
3. **Credit Integration**: ✅ Credit checks added to all step pages
4. **Project Workflow**: 🧪 **NEEDS TESTING** - Full step1-11 workflow
5. **Session Persistence**: 🧪 **NEEDS TESTING** - Project save/load functionality
6. **Delete Operations**: ✅ Project deletion working correctly
7. **Webhook Deployment**: Set up webhook endpoint for production

### 📋 TESTING CHECKLIST
- [x] Run database/projects_table.sql
- [x] Run database/transactions_table.sql
- [x] Run database/fixed_credits_usage.sql
- [x] Execute database functions
- [x] Verify foreign key constraints
- [x] Test project creation and saving
- [x] Test project opening and data restoration
- [x] Test project deletion
- [ ] **Test complete step1-11 workflow**
- [ ] **Test session state persistence across steps**
- [ ] **Test credit deduction integration**
- [ ] Configure Stripe webhooks for production
- [ ] Test payment flows end-to-end

## 7. ENVIRONMENT VARIABLES

### Required Configuration
```env
# OpenAI & APIs
OPENAI_API_KEY=your_openai_key
SERPAPI_API_KEY=your_serpapi_key
PERPLEXITY_API_KEY=your_perplexity_key
AXESSO_KEY=your_axesso_key

# Supabase
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_KEY=your_supabase_anon_key

# Stripe
STRIPE_SECRET_KEY=sk_test_4eC39HqLyjWDarjtT1zdp7dc
STRIPE_PUBLISHABLE_KEY=pk_test_your_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# App
APP_URL=http://localhost:8501
```

## 8. SECURITY CONSIDERATIONS

- API keys stored securely in environment variables
- Webhook signature validation
- RLS policies prevent unauthorized data access
- Credit operations use direct table queries (NO RPC CALLS)
- HTTPS required for production
- No sensitive data in client-side code

**⚠️ AUTHENTICATION SYSTEM CONSTRAINTS**:
- st_supabase_connection does NOT support RPC calls
- RPC calls corrupt session state and cause authentication failures
- All database operations must use direct table queries only
- Manual aggregation required for complex data operations

## 9. MONITORING & ANALYTICS

- Transaction logging for audit trails
- Credit usage analytics by user and action type
- Payment success/failure tracking
- User onboarding completion rates
- Project progress tracking

---

**Status**: Project management system completed with modular data storage. Credit integration implemented across all steps.

**Next Phase**:
1. **IMMEDIATE**: Test complete workflow from step1-11 with project saving
2. **PRIORITY**: Verify session state persistence and data restoration
3. **PRODUCTION**: Deploy webhook endpoints and conduct final e2e testing

**Key Features Ready**:
- ✅ Modular project data storage (JSONB)
- ✅ Complete step workflow with credit deduction
- ✅ Project dashboard with card layout
- ✅ Session state restoration
- ✅ Progress tracking and status management
- 🧪 **NEEDS VERIFICATION**: End-to-end workflow testing
